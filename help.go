package mnms

import (
	"fmt"
	"strings"

	"github.com/MakeNowJust/heredoc"
)

var utilImportHelpText = heredoc.Doc(`
	Usage: util import [resource] -in [in_file] -root [root_url]
		Import resource from [in_file]. If [in_file] is empty, input from stdin. 
		input file must be encrypted by public key that corresponding to the NIMBL root's
		private key. 

		Resource:
			- config: import nimbl config file

		Arguments:
			[in_file]  : input file
			[root_url] : nimbl root url

		Example :
			util import config -in nimbl.conf -root http://localhost:27182`)

var utilExportHelpText = heredoc.Doc(`
	Usage : util export [resource] -pubkey [pubkey_file] -out [out_file]
	 	Export specified resource, for secure reason NIMBL ask user to input the 
	 	public key that use to encrypt the resource. If [out_file] is empty,
	 	output to stdout. 

		Resource:
			- config: export nimbl config file, flag -pubkey is required
			- default-public-key: Export the NIMBL default public key. 
				Use with caution; intended for testing purposes only. 
				It's used for encryption when importing data without 
				a specified private key at NIMBL startup
		
		Arguments:
			[resource]    : resource name, such as 'config'.
			[pubkey_file]: public key file path
		
		Example :
			util export config -pubkey C:/Users/<USER>/nimbl.pub -out nimbl.conf.enc
			util export config -pubkey C:/Users/<USER>/nimbl.pub > nimbl.conf.enc
			util export default-public-key -out nimbl.pub
`)

var utilHelpText = heredoc.Doc(`
	NIMBL utility command.
	
	Commands:
		genpair : Generate rsa key pair.
		encrypt : Encrypt file with public key.
		decrypt : Decrypt file with private key.
		export: Export resource.
		import: Import resource.

	Type 'help util [cmd]' for more information.
`)

// utilsHelpCmd is a helper function to print help message.
func utilsHelpCmd(cmd string) string {
	words := strings.Fields(cmd)
	length := len(words)

	// help util
	if length == 2 && words[1] == "util" {
		return utilHelpText
	}
	if length < 3 {
		return fmt.Sprintf("Unknown command: %s Do you mean help util?", cmd)
	}

	switch words[2] {
	case "genpair":
		return heredoc.Doc(`
	Usage: util genpair
		Generate rsa key pair. default output is $HOME/.mnms/id_rsa 
		and $HOME/.mnms/id_rsa.pub

		Example :
		util genpair

	Usage: util genpair -name [file_prefix]
		Generate rsa key pair to [file_prefix].pub and [file_prefix] 
		
		[file_prefix]  : output file prefix (optional)

		Example :
		util  genpair -name ~/nimblkey`)

	case "encrypt":
		return heredoc.Doc(`
	Usage: util encrypt -pubkey [pubkey_file] -in [in_file] -out [out_file]
		Encrypt [in_file] with [pubkey_file] and output to [out_file].
		If [out_file] is empty, output to stdout. if [in_file] is empty, input from stdin.

		Arguments:
			[pubkey_file] : public key file
			[in_file] : The input file (optional)
			[out_file] : The output file (optional)

		Example :
			util encrypt -pubkey C:/Users/<USER>/nimbl.pub -in nimbl.conf -out nimbl.conf.enc`)

	case "decrypt":
		return heredoc.Doc(`
	Usage: util decrypt -privkey [privkey_file] -in [in_file] -out [out_file]
		Decrypt [in_file] with [privkey_file] and output to [out_file].
		If [out_file] is empty, output to stdout. if [in_file] is empty, input from stdin.

		Arguments:
			[privkey_file] : private key file
			[in_file]      : input file (optional)
			[out_file]     : output file (optional)

		Example :
			util decrypt -privkey C:/Users/<USER>/nimbl -in nimbl.conf.enc -out nimbl.conf`)

	case "export":
		return utilExportHelpText

	case "import":
		return utilImportHelpText
	default:
		return utilHelpText
	}
}

var forwardImportHelpText = heredoc.Doc(`
	Usage: forward import [url]
		[url] : URL to import forward configuration from. Make sure the forward service can access the URL.

	Example with bbctl:
		bbctl -cc service-name forward import http://example.com/forward-config.json
		bbctl -cc service-name forward import file://path/to/local/forward_config.json

	Example with Script UI (fill service name in the client field):
		forward import http://example.com/forward-config.json
		forward import file://path/to/local/forward_config.json

	Response:
		status: "ok" or "error: <error message>"

	Example full JSON config:
	{
		"whatsapp": {
			"enabled": true,
			"account_sid": "YOUR_ACCOUNT_SID",
			"auth_token": "YOUR_AUTH_TOKEN",
			"from_number": "+*********0",
			"to_numbers": [
				"+**********",
				"+1*********0"
			],
			"alert_config": {
				"min_severity": 0,
				"max_severity": 3,
				"rate_limit_seconds": 300,
				"max_alerts_per_minute": 5,
				"keywords": [
					"error",
					"fail",
					"critical",
					"emergency",
					"alert"
				],
				"exclude_keywords": [
					"debug"
				]
			}
		},
		"telegram": {
			"enabled": true,
			"bot_token": "YOUR_BOT_TOKEN",
			"chat_ids": [
				"*********",
				"@your_channel"
			],
			"alert_config": {
				"min_severity": 0,
				"max_severity": 4,
				"rate_limit_seconds": 180,
				"max_alerts_per_minute": 10,
				"keywords": [],
				"exclude_keywords": []
			}
		},
		"mqtt": {
			"enabled": false,
			"broker_host": "localhost",
			"broker_port": 1883,
			"username": "",
			"password": "",
			"topic": "mnms/alerts",
			"qos": 1,
			"retain": false,
			"alert_config": {
				"min_severity": 0,
				"max_severity": 7,
				"rate_limit_seconds": 60,
				"max_alerts_per_minute": 20,
				"keywords": [],
				"exclude_keywords": []
			}
		}
	}
`)

var forwardConfigHelpText = heredoc.Doc(`
	Usage: forward config [-option] [value]
		[-option] : Option to set in the forward configuration.
		[value]   : Value to set for the option.

	Example with bbctl:
		bbctl -cc service-name forward config -whatsapp=true -whatsapp_to_numbers=+*********0,+********** -whatsapp_keywords=error,critical -telegram=true -telegram_chat_ids=12345,67890 -mqtt=true -mqtt_broker_host=broker.example.com -mqtt_topic=alerts

	Example with Script UI (fill service name in the client field):
		forward config -whatsapp=true -whatsapp_to_numbers=+*********0,+********** -whatsapp_keywords=error,critical -telegram=true -telegram_chat_ids=12345,67890 -mqtt=true -mqtt_broker_host=broker.example.com -mqtt_topic=alerts
		
	Response:
		status: "ok" or "error: <error message>"

	Available options:
		- whatsapp=true|false
		- whatsapp_account_sid=your_account_sid
		- whatsapp_auth_token=your_auth_token
		- whatsapp_from_number=your_whatsapp_from_number
		- whatsapp_to_numbers=comma,separated,phone,numbers
		- whatsapp_min_severity=0-7 (default: 0)
		- whatsapp_max_severity=0-7 (default: 7)
		- whatsapp_rate_limit_seconds=seconds (default: 300)
		- whatsapp_max_alerts_per_minute=number (default: 5)
		- whatsapp_keywords=comma,separated,keywords (whitelist)
		- whatsapp_exclude_keywords=comma,separated,keywords (blacklist)
		- telegram=true|false
		- telegram_bot_token=your_bot_token
		- telegram_chat_ids=comma,separated,chat_ids
		- telegram_min_severity=0-7 (default: 0)
		- telegram_max_severity=0-7 (default: 7)
		- telegram_rate_limit_seconds=seconds (default: 180)
		- telegram_max_alerts_per_minute=number (default: 10)
		- telegram_keywords=comma,separated,keywords (whitelist)
		- telegram_exclude_keywords=comma,separated,keywords (blacklist)
		- mqtt=true|false
		- mqtt_broker_host=your_mqtt_broker_host (default: localhost)
		- mqtt_broker_port=your_mqtt_broker_port (default: 1883)
		- mqtt_username=your_mqtt_username
		- mqtt_password=your_mqtt_password
		- mqtt_topic=your_mqtt_topic (default: mnms/alerts)
		- mqtt_qos=0|1|2 (default: 1)
		- mqtt_retain=true|false (default: false)
		- mqtt_min_severity=0-7 (default: 0)
		- mqtt_max_severity=0-7 (default: 7)
		- mqtt_rate_limit_seconds=seconds (default: 60)
		- mqtt_max_alerts_per_minute=number (default: 20)
		- mqtt_keywords=comma,separated,keywords (whitelist)
		- mqtt_exclude_keywords=comma,separated,keywords (blacklist)
`)

var forwardRawJsonConfigHelpText = heredoc.Doc(`
	Usage: forward raw-json config [raw_json_string]
		[json_string]: should be a valid JSON string representing the ForwardConfig structure, use single quotes for the entire string if using bbctl.

	Example with bbctl:
		bbctl -cc service-name forward raw-json config '{"whatsapp": {"enabled": true, "account_sid": "your_account_sid", "auth_token": "your_auth_token", "from_number": "your_whatsapp_from_number", "to_numbers": ["+*********0"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 300, "max_alerts_per_minute": 5, "keywords": ["error", "critical"], "exclude_keywords": []}}, "telegram": {"enabled": true, "bot_token": "your_bot_token", "chat_ids": ["12345"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 180, "max_alerts_per_minute": 10, "keywords": [], "exclude_keywords": []}}, "mqtt": {"enabled": true, "broker_host": "localhost", "broker_port": 1883, "username": "", "password": "", "topic": "mnms/alerts", "qos": 1, "retain": false, "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 60, "max_alerts_per_minute": 20, "keywords": [], "exclude_keywords":[]}}}'

	Example with Script UI (fill service name in the client field):
		forward raw-json config {"whatsapp": {"enabled": true, "account_sid": "your_account_sid", "auth_token": "your_auth_token", "from_number": "your_whatsapp_from_number", "to_numbers": ["+*********0"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 300, "max_alerts_per_minute": 5, "keywords": ["error", "critical"], "exclude_keywords": []}}, "telegram": {"enabled": true, "bot_token": "your_bot_token", "chat_ids": ["12345"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 180, "max_alerts_per_minute": 10, "keywords": [], "exclude_keywords": []}}, "mqtt": {"enabled": true, "broker_host": "localhost", "broker_port": 1883, "username": "", "password": "", "topic": "mnms/alerts", "qos": 1, "retain": false, "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 60, "max_alerts_per_minute": 20, "keywords": [], "exclude_keywords":[]}}}

	Response:
		status: "ok" or "error: <error message>"

	Available options in [json_string]:
		Please refer to the help forward config command for the full JSON structure.
`)

var forwardUploadHelpText = heredoc.Doc(`
	Usage: forward upload
		Upload the current forward configuration to the Root Service.
	
	Example with bbctl:
		bbctl -cc service-name forward upload

	Example with Script UI (fill service name in the client field):
		forward upload

	Response:
		status: "ok" or "error: <error message>"
`)

var forwardSendHelpText = heredoc.Doc(`
	Usage: forward send [[facility] [severity] [tag] [message...]
		[facility]   : syslog facility (0-23), RFC3164 
		[severity]   : syslog severity (0-7), RFC3164
		[tag]        : syslog tag
		[message...] : syslog message, can be multiple words

	Example with bbctl:
		bbctl -cc service-name forward send 0 1 test alert test

	Example with Script UI (fill service name in the client field):
		forward send 0 1 test alert test

	Response:
		status: "ok" or "error: <error message>"
		result: {
			"whatsapp_result": "WhatsApp alert sent.", "WhatsApp alert not sent by config." or "WhatsApp alert disabled."
			"telegram_result": "Telegram alert sent.", "Telegram alert not sent by config." or "Telegram alert disabled."
			"mqtt_result": "MQTT alert sent.", "MQTT alert not sent by config." or "MQTT alert disabled."
			"raw_line": "<syslog message from the command line>"
		}
`)

var forwardCustomSendHelpText = heredoc.Doc(`
	Usage: forward custom send [json_string] [facility] [severity] [tag] [message...]
		[json_string] : should be a valid JSON string representing the ForwardConfig structure, use single quotes for the entire string if using bbctl.
		[facility]    : syslog facility (0-23), RFC3164
		[severity]    : syslog severity (0-7), RFC3164
		[tag]         : syslog tag
		[message...]  : syslog message, can be multiple words

	Example with bbctl:
		bbctl -cc service-name forward custom send '{"whatsapp": {...}, "telegram": {...}, "mqtt": {...}}' 1 5 mytag This is a test message

	Example with Script UI (fill service name in the client field):
		forward custom send {"whatsapp": {...}, "telegram": {...}, "mqtt": {...}} 1 5 mytag This is a test message

	Response:
		status: "ok" or "error: <error message>"
		result: {
			"whatsapp_result": "WhatsApp alert sent.", "WhatsApp alert not sent by config." or "WhatsApp alert disabled."
			"telegram_result": "Telegram alert sent.", "Telegram alert not sent by config." or "Telegram alert disabled."
			"mqtt_result": "MQTT alert sent.", "MQTT alert not sent by config." or "MQTT alert disabled."
			"raw_line": "<syslog message from the command line>"
		}

	Available options in [json_string]:
		Please refer to the help forward import command and help forward raw-json config command for the full JSON structure.
`)

var forwardHelpText = heredoc.Doc(`
	NIMBL forward command.

	Commands:
		import          : Import forward configuration from file.
		config          : Configure forward settings with flags.
		raw-json config : Configure forward settings with raw JSON.
		upload          : Upload current forward configuration to the Root Service.
		send            : Forwarder sends a syslog message within its forward configuration.
		custom send     : Forwarder sends a syslog message with custom raw-json settings.

	Type 'help forward [cmd]' for more information.
`)

func forwardsHelpCmd(cmd string) string {
	words := strings.Fields(cmd)
	length := len(words)

	// help forward
	if length == 2 && words[1] == "forward" {
		return forwardHelpText
	}
	if length < 3 {
		return fmt.Sprintf("Unknown command: %s. Do you mean help forward?", cmd)
	}

	switch words[2] {
	case "import":
		return forwardImportHelpText
	case "config":
		return forwardConfigHelpText
	case "raw-json":
		if words[3] == "config" {
			return forwardRawJsonConfigHelpText
		}
		return fmt.Sprintf("Unknown command: %s. Do you mean help forward raw-json config?", cmd)
	case "upload":
		return forwardUploadHelpText
	case "send":
		return forwardSendHelpText
	case "custom":
		if length > 3 && words[3] == "send" {
			return forwardCustomSendHelpText
		}
		return fmt.Sprintf("Unknown command: %s. Do you mean help forward custom send?", cmd)
	default:
		return forwardHelpText
	}
}

func HelpCmd(cmd string) string {
	if strings.HasPrefix(cmd, "help mtderase") {
		return `
	Erase target device mtd and restore default settings.

	Usage : mtderase [mac address]
		[mac address] : target device mac address
	Example :
		    mtderase AA-BB-CC-DD-EE-FF
		`
	}
	if strings.HasPrefix(cmd, "help beep") {
		return `
	Beep target device.

	Usage : beep [mac address]
		[mac address] : target device mac address

	Example :
		    beep AA-BB-CC-DD-EE-FF
		`
	}
	if strings.HasPrefix(cmd, "help reset") {
		return `
	Reset/Reboot target device.

	Usage : reset [mac address]
		[mac address] : target device mac address

	Example :
		    reset AA-BB-CC-DD-EE-FF
		`
	}

	if strings.HasPrefix(cmd, "help gwd") {
		return `
	Configure GWD commands

	Usage : gwd beep [mac address]
		[mac address] : target device mac address

	Example :
		    gwd beep AA-BB-CC-DD-EE-FF

	Usage : gwd reset [mac address]
		[mac address] : target device mac address

	Example :
		    gwd reset AA-BB-CC-DD-EE-FF

	Usage : gwd config network set [mac address] [ip address] [new ip] [mask] [gateway] [hostname]
        [mac address]     : target device mac address.
        [ip address]      : target device ip address.
        [new ip address]  : new IP address to assign to the device.
        [mask]            : subnet mask for the device's network.
        [gateway]         : default gateway for the device.
        [hostname]        : hostname to assign to the device.

    Example :
            gwd config network set AA-BB-CC-DD-EE-FF ********* *********0 *********** 0.0.0.0 switch 
		
		DHCP Enable case: new ip address field is must pass 0.0.0.0 as shown below
			gwd config network set AA-BB-CC-DD-EE-FF ********* 0.0.0.0 *********** 0.0.0.0 switch 
		
	Usage : gwd firmware update [mac address] [file url]
		[mac address] : target device mac address
		[file url]    : file url path

	Example :
		    gwd firmware update AA-BB-CC-DD-EE-FF file:///C:/Users/<USER>/Downloads/xxxxx.dld
			gwd firmware update AA-BB-CC-DD-EE-FF file:///C:/Users/<USER>/Downloads/xxxxx.zip

	Usage : gwd mtderase [mac address]
		[mac address] : target device mac address

	Example :
		    gwd mtderase AA-BB-CC-DD-EE-FF
		
			`
	}
	if strings.HasPrefix(cmd, "help scan") {
		return `
	Use different protocol to scan all devices.

	Usage : scan [protocol]
		[protocol]    : use gwd/snmp to scan all devices.
	Example :
		scan gwd
	
	Usage : scan cidr [options]
	       add device by IP Range (CIDR)
	[options]
		-save: save to devlist (default:false)
		-list: print it out (default:false)
	Example :
	scan ***********/24
	scan ***********/24 -save
	scan ***********/24 -save -list
		`
	}
	if strings.HasPrefix(cmd, "help device") {
		return `
	Use device to delete option for all devices and edit option for third party devices only.

	Usage : device edit [mac address] [model] [netmask] [hostname]
		[mac address] : target device mac address
		[model] : target device model
		[netmask] : target device netmask
		[hostname] : target device hostname

	Example :
		device edit AA-BB-CC-DD-EE-FF EHG75xx ************* test_switch
	
	Usage : device delete [all| mac address]
		[mac address] : target device mac address
		
	Example :
		device delete AA-BB-CC-DD-EE-FF
		device delete all
		`
	}
	if strings.HasPrefix(cmd, "help config") {
		return `
	Configure device setting.

	Usage : config user [mac address] [username] [password]

    Example:
     config user AA-BB-CC-DD-EE-FF admin default

	Usage : config network set [mac address] [ip address] [new ip] [mask] [gateway] [hostname] [dhcp] 
	[mac address]     : target device mac address.
	[ip address]      : target device ip address.
	[new ip address]  : new IP address to assign to the device.
	[mask]            : subnet mask for the device's network.
	[gateway]         : default gateway for the device.
	[hostname]        : hostname to assign to the device.
	[dhcp]            : option to enable DHCP.
								0 - Disable DHCP
								1 - Enable DHCP

    Example :
		config network set AA-BB-CC-DD-EE-FF ********* *********0 *********** 0.0.0.0 switch 0 
	

	Usage : config syslog set [mac address] [status] [server ip] [server port] [server level] [log to flash]
		[mac address] : target device mac address
		[status]      : use snmp to configure syslog enable/disable
		[server ip]   : use snmp to configure server ip address
		[server port] : use snmp to configure server port
		[server level]: use snmp to configure server log level
		[log to flash]: use snmp to configure log to flash

	Example :
		config syslog set AA-BB-CC-DD-EE-FF 1 ********* 5514 1 1

	Usage : config syslog get [mac address]
		[mac address] : target device mac address

	Example :
		config syslog get AA-BB-CC-DD-EE-FF
    
	Usage : config local syslog path [path]
		[path]        : local syslog path to file
	Example :  This is root command for this need to execute in flag(-ck) kind field add root
		-ck root config local syslog path tmp/syslog.log

	Usage : config local syslog maxsize [maxsize]
		[maxsize]     : local syslog file maxsize size
	Example :  This is root command for this need to execute in flag(-ck) kind field add root
		-ck root config local syslog maxsize 100

	Usage : config local syslog compress [compress]
		[compress]     : would be compressed
	Example : This is root command for this need to execute in flag(-ck) kind field add root
		-ck root config local syslog compress true

	Usage : config local syslog read [start date] [start time] [end date] [end time] [max line]
		[start date]   : search syslog start date
		[start time]   : search syslog start time
		[end date]     : search syslog end date
		[end time]     : search syslog end time
		[max line]     : max lines, if without max line, that mean read all of lines
	Example :  This is root command for this need to execute in flag(-ck) kind field add root
		-ck root config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00
		-ck root config local syslog read 5
		-ck root config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00 5

	Usage : config local syslog remote [server address]
		[server address] : remote syslog server address
	Example : This is root command for this need to execute in flag(-ck) kind field add root
		-ck root config local syslog remote :5514

	Usage : config local syslog backup-after-forward [enable]
		[enable]      : config local syslog bak after fwd, true/false
	Example : This is root command for this need to execute in flag(-ck) kind field add root
		-ck root config local syslog backup-after-forward true

	Usage : config save [mac address]
		[mac address] : target device mac address
	Example :
		config save AA-BB-CC-DD-EE-FF
		`
	}
	if strings.HasPrefix(cmd, "help switch") {
		return `
	Use target device CLI configuration commands.
	Note that before executing these commands first need to know the exact switch commands of the respective models, all models will not support same commands
	model to model devices varies the commands uses

	Usage : switch [mac address] [cli cmd...]
		[mac address] : target device mac address
		[cli cmd...]  : target device cli command

	Example :For EHG75xx device getting configured ip address 
		switch AA-BB-CC-DD-EE-FF show ip
		
	Usage : switch [mac address] snmp trap [server ip] [community] [server port]
        [mac address]  : target device mac address
		[server ip]   : use snmp to configure server ip address
		[server port] : use snmp to configure server port
		[community]   : snmp community

    Example :For EHG75xx device configuring the trap setting
        switch AA-BB-CC-DD-EE-FF snmp trap ********* public 162	
		
	Usage : switch [mac address] snmp enable
        [mac address]  : target device mac address
      
    Example :  For EHG75xx device configuring snmp enable
        switch AA-BB-CC-DD-EE-FF snmp enable

	Usage : switch config save [mac address] 
        [mac address]  : target device mac address
  
    Example :
        switch config save AA-BB-CC-DD-EE-FF

	Usage : switch [mac address] show snmp trap 
        [mac address]  : target device mac address
  
    Example :For EHG75xx device get trap setting
        switch AA-BB-CC-DD-EE-FF show snmp trap
		`
	}

	if strings.HasPrefix(cmd, "help snmp") {
		return `
	Use snmp get/set/update/communities/config.
    
	Usage : snmp enable [mac address]
        [mac address]  : target device mac address
    Example :
        snmp enable AA-BB-CC-DD-EE-FF

	Usage : snmp disable [mac address]
        [mac address]  : target device mac address
    Example :
        snmp disable AA-BB-CC-DD-EE-FF

	Usage : snmp trap add [mac address] [server ip] [server port] [community]
		[mac address] : target device mac address	
	    [server ip]   : use snmp to configure server ip address
		[server port] : use snmp to configure server port
		[community]   : snmp community
	Example :
		snmp trap add AA-BB-CC-DD-EE-FF ********* 162 public

    Usage : snmp trap del [mac address] [server ip] [server port] [community]
		[mac address] : target device mac address	
	    [server ip]   : use snmp to configure server ip address
		[server port] : use snmp to configure server port
		[community]   : snmp community
	Example :
		snmp trap del AA-BB-CC-DD-EE-FF ********* 162 public

     Usage : snmp trap get [mac address]
		[mac address] : target device mac address	
	Example :
		snmp trap get AA-BB-CC-DD-EE-FF
		
	Usage : snmp get [ip address] [oid]
		[ip address]  : target device ip address
		[oid]         : target oid
	Example :
		snmp get ********* *******.*******.0

	Usage : snmp set [ip address] [oid] [value] [value type]
		[ip address]  : target device ip address
		[oid]         : target oid
		[value]       : would be set value
		[value type]  : would be set value type.(OctetString, BitString, SnmpNullVar, Counter,
											Counter64, Gauge, Opaque, Integer, ObjectIdentifier, IpAddress, TimeTicks)
	Example :
		snmp set ********* *******.*******.0 www.atop.com.tw OctetString
		
	Usage: snmp communities [mac]
 		Read device's SNMP communities and update to system.

		[mac]      : Device mac address

	Example:
 		snmp communities AA-BB-CC-DD-EE-FF

	Usage: snmp update community [mac] [read community] [write community]
		Update device's SNMP communities manually.
		[mac]            : Device mac address
		[read community] : Device snmp read community
		[write community]: Device snmp write community
	Example:
		snmp update community AA-BB-CC-DD-EE-FF public private

	Usage: snmp options [port] [community] [version] [timeout]
		Update global snmp options.
		[port]     : snmp listen port
		[community]: snmp community
		[version]  : snmp version
		[timeout]  : snmp timeout
	Example:
		snmp options 161 public 2c 2	
    
    	Usage: snmp bulk [ip_address] [oid]
		[ip_address]: The IP address of the target device
		[oid]: The numeric OID representing the starting point for the bulk retrieval. The command will attempt to retrieve all subsequent OIDs within this branch until the end of the MIB view or the maximum number of repetitions is reached.
	
	Example:
		snmp bulk ********* *******.2.1.1

	Usage: snmp walk [ip_address] [oid]
		[ip_address]: The IP address of the target device.
		[oid]: The numeric OID representing the starting point of the walk (e.g., *******.2.1.2 for the interfaces group).

	Example:
		snmp walk ********* *******.2.1.2

	Usage: snmp config syslog set [MAC Address] [Enable] [Server IP] [Server Port] [Log Level] [Log to Flash]
		[mac address] : target device mac address
		[status]      : use snmp to configure syslog enable(1)/disable(2)
		[server ip]   : use snmp to configure server ip address
		[server port] : use snmp to configure server port
		[server level]: use snmp to configure server log level
		[log to flash]: use snmp to configure log to flash enable(1)/disable(2)	

	Example:
		snmp config syslog set AA-BB-CC-DD-EE-FF 1 ********* 1234 7 1
		snmp config syslog set AA-BB-CC-DD-EE-FF 2 ********* 1234 7 2

	Usage: snmp config syslog get [MAC Address]
		[mac address] : target device mac address

	Example:
		snmp config syslog get AA-BB-CC-DD-EE-FF
		`
	}
	if strings.HasPrefix(cmd, "help debug") {
		return `
	Configure debug setting.

	Commands:
	
	debug log pattern : 		The pattern accepts regular expressions, allowing you to filter debug messages based on their content.

	debug log output  :  		This command sets the destination where debug messages will be written.

	debug log off     :     	This command will stop the logging of debug messages.

	debug log clear   :   	This command will clear all existing logs in memory.

	Usage : debug log off
	Example :
		debug log off

	Usage : debug log pattern [pattern]
		[pattern]     : log pattern
	Example :
		debug log pattern .*

	Usage : debug log output [output]
		[output]      : log output
	Example :
		debug log output stderr
		`
	}

	if strings.HasPrefix(cmd, "help msg") {
		return `
	Usage : msg syslog send [facility] [severity] [tag] [message]
		[facility]    : syslog facility
		[severity]    : syslog severity
		[tag]         : syslog was sent from tag what feature name
		[message]     : would send messages
	Example :
		msg syslog send 0 1 InsertDev "new device"
		`
	}

	if strings.HasPrefix(cmd, "help syslog") {
		return `
	Usage : syslog config path [path]
	Set the path of the syslog file.
		[path]        : syslog file path
	Example :
		syslog config path /tmp/syslog.log

	Usage : syslog config maxsize [maxsize]
	Set the maxsize of the syslog file.
		[maxsize]     : syslog file maxsize size in MB
	Example :
		syslog config maxsize 100

	Usage : syslog config compress [compress]
	Set syslog file compressed or not if the file size exceeds the maxsize.
		[compress]    : syslog file compress
	Example :
		syslog config compress true

	Usage : syslog config remote [server address]
	Set syslog service remote server address.
		[server address] : remote syslog server address
	Example :
		syslog config remote :5514

	Usage : syslog config backup-after-forward [enable]
	Set syslog service backup after forwarding.
		[enable]      : syslog file bak after fwd, true/false
	Example :
		syslog config backup-after-forward true

	Usage : syslog config severity-range-forward [min severity] [max severity]
	Set syslog service severity range to forward to remote syslog server.
		[min severity] : min severity to forward to remote syslog server
		[max severity] : max severity to forward to remote syslog server
	Example :
		syslog config severity-range-forward 0 1 // send emergency and alert
		syslog config severity-range-forward -1 5 // send all except notice and debug
		syslog config severity-range-forward -1 7 // send all
		syslog config severity-range-forward -1 -1 // send nothing

	Usage : syslog config get
	Get syslog service setting into result.
	Example :
		-cc log1 syslog config get

	Usage : syslog list
	List syslog files in the syslog path and log file urls produced via syslog export command
	Result is a json object with the following structure:
	{
		"files": [syslog_mnms.log, syslog_mnms_2023-02-21T22:06:00Z.log, ...],
		"export_urls": [http://localhost:27182/api/v1/files/syslog_1709638445.log, ...]
	}

	Usage : syslog export [source filename] [spec]
	Export syslog file and return the url to access. Max size is 10MB.
	Note that the URL host is the log service's host, specify with -ns flag to change it when starting bblogsvc.
		[source filename]     : Log file name in the syslog path, can be listed by syslog list, or use all to query all log files
		[spec]                : Spec of log file, can be start date, start time, end date, end time, max line, if without max line, that mean read all of lines.
	Example :
		syslog export syslog_mnms.log
		syslog export syslog_mnms.log 5
		syslog export syslog_mnms.log 2023/02/21 22:06:00 2023/02/22 22:08:00
		syslog export syslog_mnms.log 2023/02/21 22:06:00 2023/02/22 22:08:00 5
		syslog export all 2023/02/21 22:06:00 2023/02/22 22:08:00
		syslog export all 2023/02/21 22:06:00 2023/02/22 22:08:00 100

	Usage : syslog remove [category] [filename]
	Use syslog list command to list the log files, then use syslog remove command to remove the log files.
	Use this command to remove the log files generated by syslog export command.
		[category]   : category of log files, can be syslog or exported, only exported is supported now
		[filename]   : log file name, can be all to remove all log files in
	Example :
		syslog remove exported syslog_1751012151.log
		`
	}
	if strings.HasPrefix(cmd, "help firmware") {
		return `
	Upgrade firmware.

	Usage : firmware update [mac address] [file url]
		[mac address] : target device mac address
		[file url]    : file url path
	Example :
		firmware update AA-BB-CC-DD-EE-FF file:///C:/Users/<USER>/Downloads/xxxxx.dld
		firmware update AA-BB-CC-DD-EE-FF file:///C:/Users/<USER>/Downloads/xxxxx.zip
		firmware update AA-BB-CC-DD-EE-FF file:///tmp/downloads/xxx.dld
		`
	}
	if strings.HasPrefix(cmd, "help mqtt") {
		return `
	Use mqtt to publish/subscribe/unsubscribe/list topic.

	Usage : mqtt [mqttcmd] [tcp address] [topic] [data...]
		[mqttcmd]     : pub/sub/unsub/list
										list is show all subscribe topic
		[tcp address] :	would pub/sub/unsub broker tcp address
		[topic]       : topic name
		[data...]     : data is messages, only publish use it.
	Example :
		mqtt pub ************:1883 topictest "this is messages."
		mqtt sub ************:1883 topictest
		mqtt unsub ************:1883 topictest
		mqtt list
		`
	}
	if strings.HasPrefix(cmd, "help opcua") {
		return `
	Opcua setting.
	
	Usage : opcua connect [url]	
		[url]         : connect to url
	Example :
		opcua connect opc.tcp://127.0.0.1:4840

	Usage : opcua read [node id]
		[node id]     : opcua node id
	Example :
		opcua read i=1002

	Usage : opcua browse [node id]
		[node id]     : opcua node id
	Example :
		opcua browse i=85

	Usage : opcua sub [node id]
		[node id]     : opcua node id
	Example :
		opcua sub i=1002

	Usage : opcua deletesub [sub id] [monitor id]
		[sub id]      : subscribe id
		[monitor id]  : monitored item id
	Example :
		opcua deletesub 1 1

	Usage : opcua close
	Example :
		opcua close
		`
	}
	if strings.HasPrefix(cmd, "help util") {
		return utilsHelpCmd(cmd)
	}
	if strings.HasPrefix(cmd, "help agent") {
		return `
	Use agent to control device.

	Use agent to reset devcie.
	Usage : agent reset [mac address]
		[mac address] : target device mac address
	Example :
		agent reset AA-BB-CC-DD-EE-FF

	Use agent to control snmp.
	Usage : agent snmp enable [mac address] [option]
		[mac address] : target device mac address
		[option]      : 1 or 0
	Example :
		agent snmp enable AA-BB-CC-DD-EE-FF 1

	Use agent to upgarde firmware.
		Usage : agent firmware update [mac address] [file url]
				[mac address] : target device mac address
				[file url]    : update file url path
	Example :
				agent firmware update AA-BB-CC-DD-EE-FF file:///C:/Users/<USER>/Downloads/xxxxx.dld
				agent firmware update AA-BB-CC-DD-EE-FF file:///C:/Users/<USER>/Downloads/xxxxx.zip
				agent firmware update AA-BB-CC-DD-EE-FF file:///tmp/downloads/xxx.dld

	Use agent to upgrade agent itself.
	Usage : agent agentclient upgrade [mac address] [file url] [checksum]
		[mac address] : target device mac address
		[file url]    : update file url, can put it in file server(https://root_or_client_ip/api/v1/files/)
		[checksum]    : use sha256 to verify update file
	Example :
		agent agentclient upgrade AA-BB-CC-DD-EE-FF https://*************/api/v1/files/agentclient
			12c0ced1a84158357525378ebcfa31967dd9bb3a32600602714bfe2222a1d609

	Use agent to beep devcie.
	Usage : agent beep [mac address]
		[mac address] : target device mac address
	Example :
		agent beep AA-BB-CC-DD-EE-FF

	Use agent to set syslog setting.
	Usage : agent config syslog set [mac address] [enable] [server ip] [server port] [log level] [log to flash]
		[mac address] : target device mac address
		[enable]      : enable syslog to remote
		[server ip]   : set syslog server ip
		[server port] : set syslog server port
		[log level]   : set syslog log level
		[log to flash]: set syslog lot to flash
	Example :
		agent config syslog set AA-BB-CC-DD-EE-FF 1 ************* 5514 8 1

	Use agent to get syslog setting.
	Usage : agent config syslog get [mac address]
		[mac address] : target device mac address
	Example :
		agent config syslog get AA-BB-CC-DD-EE-FF

	Use agent to set trap setting.
	Usage : agent snmp trap [trap option] [mac address] [server ip] [server port] [community]
		[mac address] : target device mac address
		[trap option] : set trap add/del		
		[server ip]   : set trap server ip
		[server port] : set trap server port
		[community]   : set trap community
	Example :
		agent snmp trap add AA-BB-CC-DD-EE-FF ************* 5162 public
		agent snmp trap del AA-BB-CC-DD-EE-FF ************* 5162 public

	Use agent to get trap setting.
	Usage : agent snmp trap get [mac address]
		[mac address] : target device mac address
	Example :
		agent snmp trap get AA-BB-CC-DD-EE-FF

	Use agent to set network setting.
	Usage : agent config network set [mac address] [ip] [mask] [gateway] [hostname] [dhcp]
		[mac address] : target device mac address
		[dhcp]        : set dhcp
		[ip]          : set ip address
		[mask]        : set netmask
		[gateway]     : set gateway
		[hostname]    : set hostname
	Example :
		agent config network set AA-BB-CC-DD-EE-FF *********** ************* 0.0.0.0 switch 1
		agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0

	Use agent to send device information to NIMBL.
	Usage : agent devinfo send [mac address]
		[mac address] : target device mac address
	Example :
		agent devinfo send AA-BB-CC-DD-EE-FF

	Use agent to send topology information to NIMBL.
	Usage : agent topologyinfo send [mac address]
		[mac address] : target device mac address
	Example :
		agent topologyinfo send AA-BB-CC-DD-EE-FF

	Use agent to set openvpn general setting.
	Usage : 
		agent openvpn set [mac address] [mode] [enable] [protocol] [port] [cipher] [hash] [compress] [auth mode] [Push LAN to clients]
		[virtual IP] [remote IP/FQDN] [local virtual IP] [remote virtual IP]

		# server, authentication mode is SSL/TLS
		agent openvpn set [mac address] server [enable] [protocol] [port] [cipher] [hash] [compress] 0 [Push LAN to clients] [virtual IP]

		# server, authentication mode is static key
		agent openvpn set [mac address] server [enable] [protocol] [port] [cipher] [hash] [compress] 1 [Push LAN to clients] [local virtual IP]
		[remote virtual IP]

		# client, authentication mode is SSL/TLS
		agent openvpn set [mac address] client [enable] [protocol] [port] [cipher] [hash] [compress] 0 [remote IP/FQDN]

		# client, authentication mode is static key
		agent openvpn set [mac address] client [enable] [protocol] [port] [cipher] [hash] [compress] 1 [remote IP/FQDN] [local virtual IP]
		[remote virtual IP]

		[mac address] : target device mac address
		[enable]      : enable openvpn server or client mode, '0' or '1'
		[mode]        : set openvpn server or client, 'server' or 'client'
		[protocol]    : set openvpn protocol, '0' or '1'(0:udp, 1:tcp)
		[cipher]      : set openvpn encryption cipher, '0' to '4'(0:blowfish, 1:AES256, 2:AES192, 3:AES128, 4:disable)
		[hash]        : set openvpn hash algorithm, '0' to '4'(0:SHA1, 1:MD5, 2:SHA256, 3:SHA512, 4:disable)
		[compress]    : set openvpn compression, '0' to '2'(0:LZ4, 1:LZO, 2:disable)
		[auth mode]   : set openvpn authentication mode, '0' or '1'(0:SSL/TLS, 1:static key),If the 'auth mode' set 'SSL/TLS',
										the 'server mode' just can set 'virtual IP'. If the 'auth mode' set 'static key', the 'server mode' can
										set 'local virtual IP' and 'remote virtual IP'. If the 'auth mode' set 'SSL/TLS', the 'client mode' just
										can set 'remote IP/FQDN'. If the 'auth mode' set 'static key', the 'client mode' can set 'local virtual IP'
										and 'remote virtual IP'
		[Push LAN to clients] : set openvpn push the LAN port subnet to the OpenVPN remote client, '0' or '1'(0:disable, 1:enable)
		[virtual IP]          : set openvpn virtual ip, only support server mode
		[remote IP/FQDN]      : set openvpn remote IP/FQDN, only support client mode
		[local virtual IP]    : set openvpn local virtual IP, only support 'auth mode' is 'static key'
		[remote virtual IP]   : set openvpn remote virtual IP, only support 'auth mode' is 'static key'

	Example :
		# server, authentication mode is SSL/TLS
		agent openvpn set AA-BB-CC-DD-EE-FF server 1 udp 1194 0 0 2 0 0 ********
		# server, authentication mode is static key
		agent openvpn set AA-BB-CC-DD-EE-FF client 1 udp 1194 0 0 2 0 *********00
		# client, authentication mode is SSL/TLS
		agent openvpn set AA-BB-CC-DD-EE-FF server 1 udp 1194 0 0 2 1 1 ******** ********
		# client, authentication mode is static key
		agent openvpn set AA-BB-CC-DD-EE-FF client 1 udp 1194 0 0 2 1 *********00 ******** ********

	Use agent to generate openvpn keys, if the openvpn server mode, the 'client name' field is empty.
	Usage : agent openvpn keys generate [mac address] [country code] [country Name] [city] [organization]
		[organizational Unit] [email Address] [client name]
		[mac address]         : target device mac address
		[country code]        : set country code, 2 word
		[country Name]        : set country name
		[city]                : set city
		[organization]        : set organization
		[organizational Unit] : set organizational unit
		[email Address]       : set email address
		[client name]         : set client openvpn key name
	Example :
		agent openvpn keys generate AA-BB-CC-DD-EE-FF TW Taiwan Taipei <NAME_EMAIL>
		agent openvpn keys generate AA-BB-CC-DD-EE-FF TW Taiwan Taipei <NAME_EMAIL> client1


	Use agent to upload openvpn keys to the agent client.
	Usage : agent openvpn keys upload [mac address] [keys] [url] [sha256]
		[mac address] : target device mac address
		[keys]        : set option, 'ca', 'serverCert', 'serverKey', 'clientCert', 'clientKey', 'staticKey' and 'dh'
		[url]         : set file url
		[sha256]      : set file sha256
	Example :
		agent openvpn keys upload AA-BB-CC-DD-EE-FF ca http://*************:27182/api/v1/files/ca.crt 
		3c933f3374c95451ea936df3bc009ed7df98f24cd0a09c40e6603d115d685e38
		agent openvpn keys upload AA-BB-CC-DD-EE-FF dh http://*************:27182/api/v1/files/dh2048.pem 
		73ef04589be955555771d6beeed930251fbb98acc446096d2844861860609eb1

	Use agent to download openvpn keys to the NIMBL api '/api/v1/agent/openvpn/keys'
	Usage : agent openvpn keys download [mac address] [client name]
		[mac address] : target device mac address
		[client name] : set client openvpn key name
	Example :
		agent openvpn keys download AA-BB-CC-DD-EE-FF client1

	Get openvpn client keys from the nimbl api '/api/v1/agent/openvpn/keys'
	Usage : agent openvpn keylist [client name]
		[client name] : set client openvpn key name
	Example :
		agent openvpn keylist client1

	Use agent to set openvpn status.
	Usage : agent openvpn status [mac address] [option]
		[mac address] : target device mac address
		[option]      : set openvppn status, 'start' or 'stop'
	Example :
		agent openvpn status AA-BB-CC-DD-EE-FF start
		agent openvpn status AA-BB-CC-DD-EE-FF stop

	Use agent to set ipsec setting
	Usage : agent ipsec set [mac address] [enable] [peer addr] [remote subnet] [local subnet] [conn type] 
		[share key] [p1 mode] [p1 dh group] [p1 encrypt] [p1 auth] [p1 lifetime] [p2 proto] [p2 fwd secrecy] 
		[p2 encrypt] [p2 auth] [p2 lifetime] [dpd action] [dpd interval] [dpd timeout]
		[mac address]    : target device mac address
		[enable]         : set ipsec enable, '0' or '1'(0:disable, 1:enable)
		[peer addr]      : set Peer Address, 'none' or 'x.x.x.x'
		[remote subnet]  : set Remote Subnet, 'none' or 'x.x.x.x/24'
		[local subnet]   : set Local Subnet, 'none' or 'x.x.x.x/24'
		[conn type]      : set Connection Type, '0' or '1'(0:Tunnel, 1:Transport)
		[share key]      : set Shared Key
		[p1 mode]        : Phase 1 SA (ISAKMP) Mode, '0' or '1'(0:Main, 1:Aggressive)
		[p1 dh group]    : Phase 1 SA (ISAKMP) DH Group, '0' or '1'(0:Group-2(1024 bit), 1:Group-5(1536 bit))
		[p1 encrypt]     : Phase 1 SA (ISAKMP) Encryption Algorithm, '0' or '1'(0:3DES, 1:AES-128)
		[p1 auth]        : Phase 1 SA (ISAKMP) Authentication Algorithm, '0' or '1'(0:MD5, 1:sha1)
		[p1 lifetime]    : Phase 1 SA (ISAKMP) SA Life Time, the default SA Life Time is 10800 seconds. The configurable
											 range for SA Life Time is between 300 to 86400 seconds.
		[p2 proto]       : Phase 2 SA Protocol, '0' or '1'(0:ESP, 1:AH)
		[p2 fwd secrecy] : Phase 2 SA Perfect Forward Secrecy, '0' to '2'(0:Disable, 1:Group-2(1024 bit), 2:Group-5(1536 bit))
		[p2 encrypt]     : Phase 2 SA Encryption Algorithm, '0' or '1'(0:3DES, 1:AES-128)
		[p2 auth]        : Phase 2 SA Authentication Algorithm, '0' or '1'(0:MD5, 1:sha1)
		[p2 lifetime]    : Phase 2 SA SA Life Time, , the default SA Life Time is 3600 seconds. The configurable range for
											 SA Life Time is between 180 to 86400 seconds.
		[dpd action]     : Dead Peer Detection Settings Action, DPD Action, '0' to '3'(0:None, 1:Hold, 2:Restart, 3:Clear)
		[dpd interval]   : DPD Interval. The DPD interval can be ranged from 1 to 65535 seconds. The default value for DPD
											 Interval is 30 seconds.
		[dpd timeout]    : DPD Timeout, the DPD Timeout value range from 1 to 65535. The default value of DPD Timeout is
											 120 seconds.
	Example :
		agent ipsec set AA-BB-CC-DD-EE-FF 1 none none none 0 secrets 0 0 0 0 10800 0 0 0 0 3600 1 30 120
		agent ipsec set AA-BB-CC-DD-EE-FF 1 *********00 ***********/24 *********/24 1 secrets 1 1 1 1 3600 1
		1 1 1 3600 1 30 120

	Use agent to set ipsec status.
	Usage : agent ipsec status [mac address] [option]
		[mac address] : target device mac address
		[option]      : set openvppn status, 'start' or 'stop'
	Example :
		agent ipsec status AA-BB-CC-DD-EE-FF start
		agent ipsec status AA-BB-CC-DD-EE-FF stop

	Use the agent command to save running config to device.
	Usage : agent config save [mac address]
		[mac address] : target device mac address
	Example :
		agent config save AA-BB-CC-DD-EE-FF

	Start reverse ssh tunnel.
	Usage: agent ssh reverse start [mac] [server hostname] [listen port] [remote port] [ssh server port]
		[mac]             : device mac address
		[server hostname] : server(root machine) hostname
		[listen port]     : listen port on root machine where the SSH server listens for connections
		[remote port]     : remote port on the client machine
		[ssh server port] : ssh server port
	Example:
		agent ssh reverse start AA-BB-CC-DD-EE-FF ******* 12345 443 22

	Stop ssh reversing in the device.
	Usage: agent ssh reverse stop [mac] [server hostname] [listen port]
		[mac]          : device mac address
		[server hostname] : server(root machine) hostname
		[listen port]  : listen port on root machine where the SSH server listens for connections
	Example:
		agent ssh reverse stop AA-BB-CC-DD-EE-FF ******* 12345

	Retrieve the last reverse ssh tunnel status after running start/stop command.
	Usage: agent ssh reverse status [mac]
		[mac]          : device mac address
	Example:
		agent ssh reverse status AA-BB-CC-DD-EE-FF

	Start a ssh tunnel between device web server and root. Nimbl will handshake with the device to get details to start the tunnel.
	Usage: agent ssh reverse websrv [mac]
		[mac]          : device mac address
	Example:
		agent ssh reverse websrv AA-BB-CC-DD-EE-FF

	Retrieve all ssh reverse connections on the device in json format.
	Usage: agent ssh reverse connections [mac]
		[mac]          : device mac address
	Example:
		agent ssh reverse connections AA-BB-CC-DD-EE-FF

	Use agent to set port enable setting.
	Usage : agent config port enable [mac address] [port name] [enable]
		[mac address] : target device mac address
		[port name]   : port name
		[enable]      : 1 or 0
	Example :
		agent config port enable AA-BB-CC-DD-EE-FF port1 1
		agent config port enable AA-BB-CC-DD-EE-FF port1 0

	Add new user. Unable to add an account with 'admin' name.
	Usage : agent config user add [mac address] [username] [passwd] [permission]
		[mac address] : target device mac address
		[username]    : user name
		[passwd]      : password
		[permission]  : user or admin
	Example :
		agent config user add AA-BB-CC-DD-EE-FF daniel daniel user
		agent config user add AA-BB-CC-DD-EE-FF daniel daniel admin

	Edit user password.
	Usage : agent config user edit [mac address] [username] [passwd] [new passwd]
		[mac address] : target device mac address
		[username]    : user name
		[passwd]      : password
		[new passwd]  : new password
	Example :
		agent config user edit AA-BB-CC-DD-EE-FF daniel daniel default

	Delete user. Unable to delete an account with 'admin' name.
	Usage : agent config user del [mac address] [username] [passwd]
		[mac address] : target device mac address
		[username]    : user name
		[passwd]      : password
	Example :
		agent config user del AA-BB-CC-DD-EE-FF daniel daniel

	Set GPS enable setting. Only some cellular devices have this feature.
	Usage : agent config gps enable [mac address] [enable]
		[mac address] : target device mac address
		[enable]      : 1 or 0
	Example :
		agent config gps enable AA-BB-CC-DD-EE-FF 1
		agent config gps enable AA-BB-CC-DD-EE-FF 0

	Use the agent command to send port and power information to the Nimbl network service.
	Usage : agent portpwinfo send [mac address]
		[mac address] : target device mac address
	Example :
		agent portpwinfo send AA-BB-CC-DD-EE-FF
	
	Use agent to mtderase the devcie.
	Usage : agent mtderase [mac address]
		[mac address] : target device mac address
	Example :
		agent mtderase AA-BB-CC-DD-EE-FF

	Update agent token manually.
	Usage : agent token refresh [mac address]
		[mac address] : target device mac address
	Example :
		agent token refresh AA-BB-CC-DD-EE-FF

	Use agent to set port alarm trigger.
	Usage : agent alarm port set [mac address] [alarm trigger] [port number]
		[mac address]   : target device mac address
		[alarm trigger] : set alarm trigger method, '0' to '3',(0:disable, 1:link down, 2:link up, 3:link down&link up)
		[port number]   : set port number, '1' to 'X' or 'all', 'X' may vary depending on the device, 'all' set all port
	Example :
		agent alarm port set AA-BB-CC-DD-EE-FF 2 1
		agent alarm port set AA-BB-CC-DD-EE-FF 3 all

	Use agent to get alarm status.
	Usage : agent alarm status [mac address]
		[mac address]   : target device mac address
	Example :
		agent alarm status AA-BB-CC-DD-EE-FF
	`
	}
	if strings.HasPrefix(cmd, "help wg") {
		return `
	Wireguard operation.

	Usage : wg config interface addresses set [address] [address...]
		[address]    : set interface address
		
	Example :
		wg config interface addresses set *********/32
		-ck root wg config interface addresses set *********/24 ********/24
	

	Usage : wg config interface listenport set [port]
		[port]       : set interface listen port

	Example :
		wg config interface listenport set 51820


	Usage : wg config interface mtu set [mtu]
		[mtu]        : set interface mtu

	Example :
		wg config interface mtu set 1420


	Usage : wg config interface dns set [dns] [dns...]
		[dns]        : set interface dns

	Example :
		wg config interface dns set *******


	Usage : wg config interface preup add [command]
		[command]    : interface preup command
		
	Example :
		wg config interface preup add echo preup


	Usage : wg config interface preup delete [index]
		[index]      : interface preup command index

	Example :
		wg config interface preup delete 0


	Usage : wg config interface postup add [command]
		[command]    : set interface postup command

	Example :
		wg config interface postup add "echo postup"


	Usage : wg config interface postup delete [index]
		[index]      : interface preup command index

	Example :
		wg config interface postup delete 0


	Usage : wg config interface predown add [command]
		[command]    : set interface predown command

	Example :
		wg config interface predown add "echo predown"


	Usage : wg config interface predown delete [index]
		[index]      : interface preup command index

	Example :
		wg config interface predown delete 0


	Usage : wg config interface postdown add [command]
		[command]    : set interface postdown command

	Example :
		wg config interface postdown add "echo postdown"


	Usage : wg config interface postdown delete [index]
		[index]      : interface preup command index

	Example :
		wg config interface postdown delete 0
		

	Usage : wg config interface set [address] [listenport...] [mtu...] [dns...]
		[address]    : set interface address
		[listenport] : set interface listen port
		[mtu]        : set interface mtu
		[dns]        : set interface dns

	Example :
		wg config interface set **********/32
		-ck root wg config interface set **********/24 55820 1400 *******


	Usage : wg config peer pubkey set [index] [pubkey]
		[index]      : set the nth peer
		[pubkey]     : set peer public key

	Example :
		wg config peer pubkey set 0 *********0


	Usage : wg config peer allowedips set [index] [address] [address...]
		[index]      : set the nth peer
		[address]    : set peer allowed ips

	Example :
		wg config peer allowedips set 0 **********/32


	Usage : wg config peer endpoint set [index] [endpoint]
		[index]      : set the nth peer
		[endpoint]   : set peer endpoint

	Example :
		wg config peer endpoint set ***************:55820


	Usage : wg config peer persistentkeepalive set [index] [persistentkeepalive]
		[index]              : set the nth peer
		[persistentkeepalive]: set peer persistent keepalive

	Example :
		wg config peer persistentkeepalive set 0 25


	Usage : wg config peer presharedkey set [index] [presharedkey]
		[index]       : set the nth peer
		[presharedkey]: set peer preshared key

	Example :
		wg config peer presharedkey set 0 *********0


	Usage : wg config peer add [pubkey] [allowedips] [endpoint...] [persistentkeepalive...] [presharedkey...]
		[pubkey]             : set peer public key
		[allowedips]         : set peer allowed ips
		[endpoint]           : set peer endpoint
		[persistentkeepalive]: set peer persistent keepalive
		[presharedkey]       : set peer preshared key

	Example :
		wg config peer add *********0 **********/24 ***************:55820 30


	Usage : wg config peer delete [index]
		[index]      : delete the nth peer

	Example :
		wg config peer delete 0
		-ck root wg config peer delete 1


	Usage : wg config generate

	Example :
		wg config generate
		-ck root wg config generate

	Usage : wg start
		
	Example :
		wg start
		-ck root wg start

	Usage : wg stop

	Example :
		wg stop
		-ck root wg stop
		`
	}

	if strings.HasPrefix(cmd, "help idps") {
		return `
Intrusion Detection and Prevention System

Use of idps commands 
	Usage :-cc [clientname] idps rules import [url] 
		[url]: http://xxx
		[clientname]: client1
	Description:import rules to client	
	Example :
	-cc client idps rules import http://xxx

	Usage : -cc [clientname]  idps rules delete [name]
		[name]: selftest_icmp 
		[clientname]: client1
	Description:delete rules for client
	Example:
	-cc client1 idps rules delete  selftest_icmp
	-cc client1 idps rules delete  selftest_tcp

	
	Usage :-cc [clientname] idps rules add [category] [rule]
       		[clientname]: client1
        	[category]: name
        	[{rule}]: rule
    	Description:add rules into category
    	Example:
    	-cc client1 idps rules add icmp_category drop icmp $EXTERNAL_NET any <> $HOME_NET any (msg:"icmpv4 selftest drop";sid:789;)
	
	Usage :-cc [clientname] idps record delete 
			[clientname]: client1
			-f: file name of records(option)
			-d: date(format:2024-08-09)(option)
 	Description:delete record

 	Example :
		Description: delete file name: alert.log data:2024-08-12
		-cc client1 idps records delete -f alert.log -d 2024-08-12

		Description: delete all of file on 2024-08-12
		-cc client1 idps records delete -d 2024-08-12

		Description: delete all of file
		-cc client1 idps records delete


	Usage :-cc [clientname] idps records search
			[clientname]: client1
			-f: file name of record
			-st: start time (format:2006-01-02-15:04)
			-et end time (format:2006-01-02-15:04)
			Description:search record
	Example:
	-cc client1 idps records search -f alert.log -st 2024-08-14-15:04 -et 2024-09-14-15:04
	`

	}

	if strings.HasPrefix(cmd, "help ssh") {
		return `
	SSH commands requires to run kind(-ck) is root

	Usage: ssh tunnel close [port]
	Close an SSH tunnel connection with the specified port the SSH server is listening on.
		[port]     : port number
	Example:
		 $ bbctl -ck root ssh tunnel close 2222

	Usage: ssh tunnel fetch [port]
	Send an HTTP request to the SSH tunnel.
		[port]     : port number
	Example:
		 $ bbctl -ck root ssh tunnel fetch 2222

	Usage: ssh tunnels list
	List all SSH tunnels.
	Example:
		 $ bbctl -ck root ssh tunnels list
		`
	}

	if strings.HasPrefix(cmd, "help firewall") {
		return `
	Firewall commands. Note that the firewall commands are only available on Linux with ufw installed or Windows with netsh installed.

	Usage: firewall allow [port] [...protocol]
	Add port and protocol to allow list.
		[port]     : port number
		[protocol] : protocol
	Example:
		firewall allow 80 tcp
		firewall allow 443

	Usage: firewall deny [port] [...protocol]
	Add port and protocol to deny list.
		[port]     : port number
		[protocol] : protocol
	Example:
		firewall deny 80 tcp
		firewall deny 443

	Usage: firewall remove [port] [...protocol]
	Remove port and protocol from allow/deny list.
		[port]     : port number
		[protocol] : protocol
	Example:
		firewall remove 80 tcp
		firewall remove 443

	Usage: firewall list
	List all allow/deny ports and protocols.
	Example:
		firewall list
		`
	}

	if strings.HasPrefix(cmd, "help service") {
		return `
	The NIMBL support users to manually run commands to update the service.

	Update service to latest version. When using this command, remember to add -cc or -ck.
	Usage: -ck [root service kind] service update
	       -cc [other service name] service update
		[root service kind]  : If your target is root service, requires to set kind(-ck) to root
		[other service name] : If your target is other services, requires to set service name(-cc)
	Example:
		# For root service
		-ck root service update
		# For other service
		-cc nms1 service update

	Stop service. When using this command, remember to add -cc or -ck.
	Usage: -ck [root service kind] service stop
	       -cc [other service name] service stop
		[root service kind]  : If your target is root service, requires to set kind(-ck) to root
		[other service name] : If your target is other services, requires to set service name(-cc)
	Example:
		# For root service
		-ck root service stop
		# For other service
		-cc nms1 service stop
		`
	}

	if strings.HasPrefix(cmd, "help forward") {
		return forwardsHelpCmd(cmd)
	}

	if strings.HasPrefix(cmd, "help cmd") {
		return `
	BBCTL cmd tool, list or clear commands in the NIMBL system.

	Usage: bbctl cmd list [all|<command>]
		[all|<command>] : List all commands or a specific command.
						  Note that the specific command should be the same as the one listed by 'bbctl cmd list'.
	
	Example:
		bbctl cmd list all
		bbctl cmd list gwd mtderasa 11-22-33-44-55-66

	Usage: bbctl cmd clear [all|<command>]
		[all|<command>] : Clear all commands or a specific command. 
		                  Note that please be careful when using 'all' option, it will clear all commands in the NIMBL system.
						  Also, the specific command should be the same as the one listed by 'bbctl cmd list'.

	Example:
		bbctl cmd clear all
		bbctl cmd clear gwd mtderase 11-22-33-44-55-66
		`
	}

	if strings.HasPrefix(cmd, "help") {
		msg := ""
		msg = fmt.Sprintf(" %s\n", "NIMBL API Command Usage:")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help mtderase", "Erase target device mtd and restore default settings.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help beep", "Beep target device.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help reset", "Reset/Reboot target device.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help gwd", "Configure GWD commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help scan", "Use different protocol to scan all devices.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help config", "Configure device setting.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help switch", "Use target device CLI configuration commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help snmp", "Use snmp get/set.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help syslog", "Log service commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help debug", "Configure debug setting.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help firmware", "Upgrade firmware.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help mqtt", "Use mqtt to publish/subscribe topic.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help opcua", "Opcua setting.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help util", "Utilities commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help wg", "Wireguard commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help agent", "Use agent to control device.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help idps", "IDPS commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help msg", "Msg commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help ssh", "SSH commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help firewall", "Firewall commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help service", "Service commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help device", "Device Delete/Edit.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help forward", "Forward service commands.")
		msg = msg + "\n"
		msg = msg + fmt.Sprintf(" %s\n", "BBCTL Usage:")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help cmd", "BBCTL cmd tool, list or clear commands in the NIMBL system.")

		return msg
	}
	return "error: invalid command"
}
