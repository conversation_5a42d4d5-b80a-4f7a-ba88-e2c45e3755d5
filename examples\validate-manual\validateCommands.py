import os
import re
import json
import shutil
import stat
import subprocess
import time
from datetime import datetime
import openai
from dotenv import load_dotenv

load_dotenv()

# ==============================================================================
# 1. CONFIGURATION SECTION
# ==============================================================================
GIT_REPO_URL = "**************:bbtechhive/userguide.git"
REPO_LOCAL_DIR = "./userguide_repo"
MANUAL_DIRECTORY_IN_REPO = "user_guide_chapters"
REPORT_FILENAME = "command_validation_report_{timestamp}.md"

# --- OpenAI Configuration ---
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = "gpt-4o"

# --- CLI Executables Configuration ---
SPECIAL_CLI_EXECUTABLE = {
    "path": "../../bbctl/bbctl",
    "help_arg": "help"
}
REGULAR_CLI_EXECUTABLES = [
    {"path": "../../bbrootsvc/bbrootsvc", "help_arg": "-h"},
    {"path": "../../bbnmssvc/bbnmssvc", "help_arg": "-h"},
    {"path": "../../bblogsvc/bblogsvc", "help_arg": "-h"},
    {"path": "../../bbidpsvc/bbidpsvc", "help_arg": "-h"}
]
CLI_HELP_FILE = "./cli_help_output.txt"

# --- Extraction and Analysis Configuration ---
COMMAND_EXAMPLE_REGEX = r"```(?:\w*\n)?(.*?)```"
# Since context is now included, each request token count will increase. 
# It's advisable to slightly reduce the chunk size.
CHUNK_SIZE = 20 
# Define the context window size (characters before and after the command block).
CONTEXT_WINDOW_SIZE = 500

# ==============================================================================
# 2. CORE LOGIC SECTION
# ==============================================================================
def handle_remove_readonly(func, path, exc):
    """Error handler for shutil.rmtree to handle read-only files."""
    excvalue = exc[1]
    if func in (os.rmdir, os.remove, os.unlink) and excvalue.errno == 13:
        os.chmod(path, stat.S_IWUSR)
        func(path)
    else:
        raise exc

def setup_git_repository(repo_url, local_dir):
    """Clones a Git repository, ensuring any previous local copy is removed."""
    print(f"🔄 Ensuring fresh copy of Git repository from {repo_url}...")
    if os.path.exists(local_dir):
        try:
            shutil.rmtree(local_dir, onerror=handle_remove_readonly)
            print(f"✅ Successfully removed old directory.")
        except OSError as e:
            print(f"❌ ERROR: Failed to remove directory '{local_dir}'. Error: {e}"); return False
    try:
        subprocess.run(['git', 'clone', repo_url, local_dir], check=True, capture_output=True, text=True, timeout=60)
        print("✅ Successfully cloned repository.")
    except subprocess.CalledProcessError as e:
        print(f"❌ ERROR: Failed to clone Git repository. Git Error: {e.stderr}"); return False
    return True

def generate_cli_help_file(special_cli, regular_clis, output_file):
    """
    Generates a comprehensive help file by recursively discovering and fetching
    help for a special executable with nested sub-commands, and also processing
    regular executables. This version uses robust row-by-row parsing.
    """
    print(f"🔍 Auto-discovering commands from multiple executables...")
    all_help_text = []
    processed_commands = set() # To avoid redundant fetching

    # --- Helper function for recursive discovery ---
    def discover_and_fetch_help(binary_path, help_arg, command_parts, all_help_text_list):
        """
        Recursively fetches help. command_parts is a list like ['util'] or ['util', 'genpair'].
        """
        command_key = " ".join([binary_path, help_arg] + command_parts)
        if command_key in processed_commands:
            return
        processed_commands.add(command_key)

        full_command_str_for_exec = f"{os.path.basename(binary_path)} {help_arg} {' '.join(command_parts)}"
        full_command_str_for_log = f"{os.path.basename(binary_path)} {' '.join(command_parts)}"
        command_to_run = [binary_path, help_arg] + command_parts

        print(f"   -> Fetching help for '{full_command_str_for_exec}'...")
        try:
            result = subprocess.run(command_to_run, capture_output=True, text=True, encoding='utf-8', timeout=60)
            # Normalize line endings to handle potential CRLF issues
            help_output = result.stdout.replace('\r\n', '\n') if result.stdout else (result.stderr.replace('\r\n', '\n') if result.stderr else "")

            if not help_output:
                print(f"   -> ⚠️ No help output for '{full_command_str_for_exec}'.")
                return

            all_help_text_list.append(f"\n--- Help for {full_command_str_for_log} ---\n{help_output}")

            # RECURSIVE STEP: Check for hints of deeper commands.
            if f"Type 'help {' '.join(command_parts)} [cmd]'" in help_output:
                base_command_str = ' '.join(command_parts)
                
                # *** THE ABSOLUTE FINAL FIX: ROW-BASED PARSING ***
                nested_commands = []
                in_command_section = False
                for line in help_output.split('\n'):
                    stripped_line = line.strip()
                    # Section starts with "Commands:"
                    if stripped_line.lower() == 'commands:':
                        in_command_section = True
                        continue
                    
                    # If we are in the command section and the line is not empty
                    if in_command_section and stripped_line:
                        # A command line looks like "command : description"
                        if ':' in stripped_line:
                            command_part = stripped_line.split(':', 1)[0].strip()
                            # Ensure it's a single word, to avoid multi-word descriptions
                            if ' ' not in command_part and command_part:
                                nested_commands.append(command_part)
                        else:
                            # If we hit a line without a colon, the command list likely ended
                            in_command_section = False

                if nested_commands:
                    print(f"   -> ✅ Found nested commands under '{base_command_str}': {', '.join(nested_commands)}")
                    base_command_parts = base_command_str.split()
                    for nested_cmd in nested_commands:
                        discover_and_fetch_help(binary_path, help_arg, base_command_parts + [nested_cmd], all_help_text_list)

        except Exception as e:
            print(f"   -> ⚠️ Warning: Failed to get help for '{full_command_str_for_exec}'. Error: {e}. Skipping.")


    # --- Part 1: Process the Special CLI (bbctl) with recursion ---
    if special_cli:
        binary_path = special_cli["path"]
        help_arg = special_cli["help_arg"]
        binary_name = os.path.basename(binary_path)
        print(f"\n--- Processing special executable: {binary_name} ---")

        # Start with the top-level help command
        command = [binary_path, help_arg]
        main_help_text = None
        try:
            result = subprocess.run(command, capture_output=True, text=True, encoding='utf-8', timeout=60)
            # Normalize line endings
            output = result.stdout.replace('\r\n', '\n') if result.stdout else (result.stderr.replace('\r\n', '\n') if result.stderr else "")
            
            if output:
                main_help_text = output
                print(f"✅ Successfully fetched main help for '{binary_name}'.")
                all_help_text.append(f"--- Help for {binary_name} ---\n{main_help_text}")

                # Find initial top-level sub-commands from the main help text
                top_level_sub_commands = []
                for line in main_help_text.split('\n'):
                    stripped = line.strip()
                    if stripped.startswith('help '):
                        parts = stripped.split()
                        if len(parts) > 1:
                            top_level_sub_commands.append(parts[1])
                
                # Remove duplicates and sort
                top_level_sub_commands = sorted(list(set(top_level_sub_commands)))

                if top_level_sub_commands:
                    print(f"✅ Found {len(top_level_sub_commands)} top-level sub-commands: {', '.join(top_level_sub_commands)}")
                    for cmd in top_level_sub_commands:
                        discover_and_fetch_help(binary_path, help_arg, [cmd], all_help_text)
        except FileNotFoundError:
            print(f"❌ ERROR: Special command not found at '{binary_path}'. Skipping.")
        except Exception as e:
            print(f"❌ ERROR: Failed to process special command '{binary_name}'. Error: {e}")

    # --- Part 2: Process the Regular CLIs (unchanged logic) ---
    for cli in regular_clis:
        binary_path = cli["path"]
        help_arg = cli["help_arg"]
        binary_name = os.path.basename(binary_path)
        print(f"\n--- Processing regular executable: {binary_name} ---")
        command = [binary_path, help_arg]
        try:
            result = subprocess.run(command, capture_output=True, text=True, encoding='utf-8', timeout=60)
            output = result.stdout if result.stdout else result.stderr
            if output:
                print(f"✅ Successfully fetched help for '{binary_name}'.")
                all_help_text.append(f"--- Help for {binary_name} ---\n{output}")
            else:
                print(f"   -> ⚠️ Could not get any help output for '{binary_name}'.")
        except FileNotFoundError:
            print(f"❌ ERROR: Command not found at '{binary_path}'. Skipping.")
        except Exception as e:
            print(f"   -> ❌ ERROR: Failed to process '{binary_name}'. Error: {e}")


    if not all_help_text:
        print("❌ FATAL ERROR: Failed to generate any help text from the provided executables.")
        return False

    try:
        with open(output_file, 'w', encoding='utf-8', newline='\n') as f: # Use newline='\n' for consistency
            f.write("\n\n".join(all_help_text))
        print(f"\n✅ Successfully generated a comprehensive help file at '{output_file}'")
        return True
    except IOError as e:
        print(f"❌ ERROR: Could not write to help file '{output_file}'. Error: {e}")
        return False


# Extracts commands, their source file, and most importantly, their "context".
def extract_examples_with_context(directory, pattern, context_window):
    """
    Walks through a directory, reads each Markdown file, and extracts command examples.
    For each example, it extracts not only the command but also its source file and
    the surrounding text (context).
    """
    all_examples, file_count = [], 0
    print(f"📖 Searching for Markdown (.md) files in '{directory}' and extracting commands with context...")
    if not os.path.isdir(directory):
        print(f"❌ ERROR: Directory not found: {directory}")
        return None

    for root, _, files in os.walk(directory):
        for file in sorted(files):
            if file.endswith('.md'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        # Use re.finditer to get the match object's position.
                        for match in re.finditer(pattern, content, re.DOTALL):
                            command_text = match.group(1).strip()
                            if not command_text or command_text.startswith('{'):
                                continue

                            # Calculate the start and end positions for the context slice.
                            context_start = max(0, match.start() - context_window)
                            context_end = min(len(content), match.end() + context_window)
                            
                            # Extract the context.
                            context_text = content[context_start:context_end]
                            
                            all_examples.append({
                                "command": command_text,
                                "source_file": file,
                                "context": context_text  # Add the context to the data.
                            })
                    file_count += 1
                except Exception as e:
                    print(f"⚠️ Warning: Could not read or process file {file_path}. Error: {e}")

    if not all_examples:
        print("❌ Could not find any command examples in the manual to validate.")
        return None

    print(f"✅ Found and extracted {len(all_examples)} command examples with context from {file_count} Markdown files.")
    return {"examples": all_examples, "file_count": file_count}


def read_cli_help_file(filepath):
    """Reads the definitive CLI help text from the specified file."""
    print(f"📖 Reading definitive CLI help text from '{filepath}'...")
    try:
        with open(filepath, 'r', encoding='utf--8') as f: return f.read()
    except FileNotFoundError:
        print(f"❌ FATAL ERROR: The CLI help file '{filepath}' was not found."); return None


def analyze_command_chunk(manual_chunk, cli_help_text, client, model_name):
    """
    Analyzes a chunk of examples (each with command, source, and context) using the AI
    to detect discrepancies.
    """

    system_prompt = f"""
    You are an extremely precise technical documentation reviewer. Your task is to find SIGNIFICANT, FUNCTIONAL ERRORS in user manual command examples by comparing them against the definitive help text. The "Definitive Help Text" is the absolute source of truth.

    For each item in "Manual Examples with Context to Validate", review both its "command" and its "context".

    **CORE TASK:**
    Use the provided "context" to understand the *intent* of the "command". For example, the context might explain that the command is a correct usage example, or it might intentionally show an incorrect example to warn the user.

    Based on the context and the help text, check for these specific error types:
    1.  **Invalid Command/Sub-command:** The executable (e.g., `bbrootsvc`) or the core command sequence (e.g., `agent openvpn keylist`) is completely absent from ALL "Usage:" lines in the entire help text.
    2.  **Incorrect Arguments/Flags:** A flag (e.g., `-config`) is used where it's not supported, or a required flag argument is missing.
    3.  **Structural Mismatch:** The command's structure is fundamentally broken, such as missing a required non-optional argument.

    **CRITICAL REVIEW GUIDELINES:**
    1.  **TRUST ALL 'USAGE' LINES:** The help text may contain multiple "Usage:" lines for the same base command. You MUST consider all of them. A manual example is valid if it matches ANY of the documented "Usage:" formats.
    2.  **TREAT `[]` and `{{}}` AS VALID PLACEHOLDERS:** If a part of a command in the manual example is enclosed in square brackets `[like_this]` or curly braces `{{like_this}}`, you MUST treat it as a valid, user-fillable placeholder. Do NOT flag it as an error.
    3.  **CONTEXT IS KEY:** If the context explicitly states "The following is an incorrect example...", then even if the command is wrong, you should NOT report it as a documentation error. Your goal is to find instances where the documentation *misleadingly presents a broken command as a correct one*.
    4.  **VERIFY BEFORE REPORTING:** Before flagging a command as "Invalid", you must meticulously search the ENTIRE help text.
    5.  **IGNORE GENERIC COMMANDS & COMMENTS:** Ignore shell commands like `cd`, `unzip`, and any lines that are clearly comments (e.g., starting with `#`).

    **OUTPUT FORMAT:**
    Provide your findings as a single JSON object with a key "discrepancies". This list should ONLY contain objects for commands with genuine, functional errors. If you find no errors, return an empty list. Each object must have "manual_example" (the command string only), "issue_type", and "description". In your "description", you can briefly mention how the context influenced your judgment.
    """
    
    user_prompt = f"""
    --- Definitive Help Text (Source of Truth for ALL executables) ---
    {cli_help_text}

    --- Manual Examples with Context to Validate (This Chunk Only) ---
    {json.dumps(manual_chunk, indent=2, ensure_ascii=False)}
    """
    
    try:
        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.0,
            response_format={"type": "json_object"} # Recommended: Use JSON mode for reliable output.
        )
        response_text = response.choices[0].message.content
        return json.loads(response_text)
    except json.JSONDecodeError as json_err:
        print(f"    -> ❌ Failed to parse JSON from API response: {json_err}")
        print(f"    -> Raw response was:\n{response_text}")
        return None
    except openai.RateLimitError:
        print("    -> Rate limit hit. Waiting for 60 seconds before retrying..."); time.sleep(60); return "retry"
    except Exception as e:
        print(f"    -> ❌ Chunk analysis failed: {e}")
        return None

def save_command_report(filename, results, summary):
    """Saves the validation report to a Markdown file, including context for each error."""
    print(f"\n💾 Saving command validation report to {filename}...")
    error_discrepancies = results.get('discrepancies', [])
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# AI Command and Context Validation Report\n\n")
            f.write(f"**Generated on:** {summary['timestamp']}\n\n")
            f.write("## Analysis Summary\n\n")
            f.write(f"- **User Guide Files Scanned:** {summary['manual_files_count']}\n")
            f.write(f"- **Command Examples Analyzed:** {summary['examples_analyzed_count']}\n")
            f.write(f"- **Total Errors Found:** {len(error_discrepancies)}\n")
            f.write(f"- **AI Model Used:** {summary.get('model_used', 'N/A')}\n\n")
            f.write("---\n\n")
            if not error_discrepancies:
                f.write("## ✅ Excellent! No command or flow errors were found in the user manual.\n")
            else:
                f.write(f"## 📋 Detailed Errors Found ({len(error_discrepancies)} issues)\n\n")
                for item in error_discrepancies:
                    f.write(f"### Error in command: `{item.get('manual_example', 'N/A').replace('`', '').splitlines()[0]}`\n\n")
                    f.write(f"- **Source Chapter:** `{item.get('source_file', 'Unknown')}`\n")
                    f.write(f"- **Issue Type:** {item.get('issue_type', 'Unknown')}\n")
                    f.write(f"- **AI's Analysis:** {item.get('description', 'No description provided.')}\n\n")
                    f.write(f"**Full command example from manual:**\n")
                    f.write(f"```\n{item.get('manual_example', 'N/A')}\n```\n\n")
                    # f.write(f"**Relevant Context:**\n")
                    # f.write(f"```text\n{item.get('context', 'No context available.')}\n```\n\n")
                    f.write("---\n")
        print(f"✅ Report saved successfully.")
    except Exception as e:
        print(f"❌ ERROR: Failed to save report. Error: {e}")

# ==============================================================================
# 3. MAIN EXECUTION BLOCK
# ==============================================================================
if __name__ == "__main__":
    # To run this script, ensure the following functions are fully implemented or uncomment the real calls.
    if not generate_cli_help_file(SPECIAL_CLI_EXECUTABLE, REGULAR_CLI_EXECUTABLES, CLI_HELP_FILE): exit(1)
    if not setup_git_repository(GIT_REPO_URL, REPO_LOCAL_DIR): exit(1)

    manual_directory_path = os.path.join(REPO_LOCAL_DIR, MANUAL_DIRECTORY_IN_REPO)
    
    # [MODIFIED] Use the new function to extract examples with context.
    manual_data = extract_examples_with_context(manual_directory_path, COMMAND_EXAMPLE_REGEX, CONTEXT_WINDOW_SIZE)
    cli_help_text = read_cli_help_file(CLI_HELP_FILE)

    if manual_data and cli_help_text:
        # This is a list of dicts, each containing command, source_file, and context.
        manual_examples = manual_data["examples"] 
            
        example_chunks = [manual_examples[i:i + CHUNK_SIZE] for i in range(0, len(manual_examples), CHUNK_SIZE)]
        total_discrepancies = []
        
        print(f"\n🤖 Splitting {len(manual_examples)} examples into {len(example_chunks)} chunks of size ~{CHUNK_SIZE}.")
        
        if not OPENAI_API_KEY:
            print("❌ ERROR: OpenAI API Key (OPENAI_API_KEY) is not set."), exit(1)
        
        try:
            client = openai.OpenAI(api_key=OPENAI_API_KEY)
            print(f"✅ OpenAI Client Initialized. Using model: {OPENAI_MODEL}")
        except Exception as e:
            print(f"❌ ERROR: Failed to initialize OpenAI Client: {e}"), exit(1)

        for i, chunk in enumerate(example_chunks):
            print(f"--- Analyzing chunk {i+1}/{len(example_chunks)} ---")
            retry_count, max_retries = 0, 2
            while retry_count < max_retries:
                result = analyze_command_chunk(chunk, cli_help_text, client, OPENAI_MODEL)
                if result != "retry": break
                retry_count += 1
            
            if result and "discrepancies" in result:
                found_discrepancies = result["discrepancies"]
                print(f"    -> ✅ Chunk {i+1} processed successfully, found {len(found_discrepancies)} errors.")
                
                # Merge the AI's findings with the original context and source_file from the chunk.
                for discrepancy in found_discrepancies:
                    # Find the corresponding original example in the chunk based on the command string returned by the AI.
                    for original_example in chunk:
                        if original_example["command"] == discrepancy["manual_example"]:
                            discrepancy["source_file"] = original_example["source_file"]
                            discrepancy["context"] = original_example["context"]
                            break # Found match, move to the next discrepancy.
                
                total_discrepancies.extend(found_discrepancies)
            else:
                 print(f"    -> ⚠️ Could not process chunk {i+1} after retries. Skipping.")
            
            if i < len(example_chunks) - 1: time.sleep(2) # Brief pause to avoid rate limiting.

        final_results = {"discrepancies": total_discrepancies}
        timestamp = datetime.now()
        final_report_filename = REPORT_FILENAME.format(timestamp=timestamp.strftime("%Y-%m-%d_%H%M%S"))
        summary_data = {
            "timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "manual_files_count": manual_data["file_count"],
            "examples_analyzed_count": len(manual_examples),
            "model_used": OPENAI_MODEL
        }
        save_command_report(final_report_filename, final_results, summary_data)
        
        print("\n" + "="*40 + "\n🔬 FINAL REPORT SUMMARY (also saved to file)\n" + "="*40)
        print(f"Total errors found: {len(total_discrepancies)}")
        print("See " + final_report_filename + " for full details.")
    else:
        print("\n❌ Analysis aborted due to missing manual data or CLI help text.")