package mnms

import (
	"fmt"
	"os/exec"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	"github.com/qeof/q"
)

func checkCloudVersionTest() error {
	QC.Version = "v1.0.2"
	_, err := CheckCloudVersion()
	if err != nil {
		return fmt.Errorf("CheckCloudVersion failed, should update to last version.")
	}
	return nil
}

func checkRootSvcVersionTest() error {
	q.P = ".*"
	// kill services
	killNmsctlProcesses()
	// run root
	var exe string
	if runtime.GOOS == "windows" {
		exe = ".exe"
	}
	rootsvcPath := filepath.Join("bbrootsvc", "bbrootsvc"+exe)

	cmd := exec.Command(rootsvcPath, "-n", "root")
	err := cmd.Start()
	if err != nil {
		fmt.Printf("Failed starting bbrootsvc: %s\n", err)
		return err
	}
	defer func() {
		q.P = ""
		killNmsctlProcesses()
	}()
	time.Sleep(5 * time.Second)

	admintoken, err := GetToken("admin")
	if err != nil {
		return err
	}

	// test CheckRootSvcVersion
	QC.RootURL = "http://localhost:27182"
	QC.AdminToken = admintoken
	QC.Version = "v1.0.2"
	_, err = CheckRootSvcVersion()
	if err != nil {
		return fmt.Errorf("CheckRootSvcVersion failed, should update to last version.")
	}
	return nil
}

func TestAutoUpdateSvc(t *testing.T) {
	type TestFunction struct {
		name string
		fn   func() error
	}
	tests := []TestFunction{
		{"checkCloudVersionTest", checkCloudVersionTest},
		{"checkRootSvcVersionTest", checkRootSvcVersionTest},
	}

	for _, test := range tests {
		err := test.fn()
		if err == nil {
			t.Logf("%v success", test.name)
		} else {
			t.Fatalf("%v failed, error : %v", test.name, err)
		}
	}

}
