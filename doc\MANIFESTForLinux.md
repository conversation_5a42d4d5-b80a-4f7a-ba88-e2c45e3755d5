# Table of contents in this ZIP installation file

## MANIFEST.md

This file.

## README.md

Basic information for the users. Please read this first.

## CHANGELOG.md

List of changes in each released version of Nimbl software

## authentication.md

Information about login, password, encryption and RSA keys.

## Blackbear_NIMBL_User_Manual.pdf

User manual for NIMBL software

## API.txt

Information about REST API and Command API 

## COPYRIGHT.txt

Copyright information

## caddy

<PERSON> executables for Linux.
Caddy is useful for running a TLS terminating reverse-proxy front for Root Nimbl server.

## bbrootsvc

NIMBL root service. This executable now combines the functionalities of both the frontend (previously `bbnimbl.exe`) and the root service, providing a unified application for better efficiency and deployment.

## bbnmssvc

NIMBL network service.

## bbctl

NIMBL command line interface.

## bblogsvc

NIMBL log service.

## bbidpsvc

NIMBL idp service.

## bbfwdsvc

NIMBL syslog forwarder service.

## bbpollsvc

NIMBL device polling service.

## get-machine-id

Return current machine id that can be used for license registration

## install_lib.sh

Install all linux libraries at once before running Nimbl software
