import React, { useState } from "react";
import dayjs from "dayjs";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  <PERSON>lapse,
  DatePicker,
  Divider,
  Flex,
  Input,
  Pagination,
  Popover,
  Radio,
  Result,
  Row,
  Space,
  Spin,
  Typography,
  theme,
} from "antd";
import RcResizeObserver from "rc-resize-observer";
import { useGetIdpsReportQuery } from "../../app/services/idpsApi";
import CategoriesTable from "../../components/dashboard/CategoriesTable";
import {
  DeleteOutlined,
  FilterOutlined,
  InboxOutlined,
} from "@ant-design/icons";
import IdpsEventTable from "../../components/dashboard/IdpsEventTable";
import IdpsPieChart from "../../components/dashboard/IDPSPieChart";
import { StatisticCard } from "@ant-design/pro-components";
import IdpsColumnChart from "../../components/dashboard/IdpsColumnChart";
import {
  idpsSelector,
  setSelectedRecordDate,
} from "../../features/dashboard/idpsSlice";
import { useDispatch, useSelector } from "react-redux";
import { useSendCommandMutation } from "../../app/services/commandApi";
import EventListCard from "../../components/dashboard/EventListCard";

const IdpsPage = () => {
  const [responsive, setResponsive] = useState(false);
  const dispatch = useDispatch();
  const { RangePicker } = DatePicker;
  const [selectedRulename, setSelectedRulename] = useState("");
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [openPopfilter, setOpenPopfilter] = useState(false);
  const [openPopdelete, setOpenPopDelete] = useState(false);
  const { selectedRecordDate } = useSelector(idpsSelector);
  const [eventFilterFileName, setEventFilterFileName] = useState("");
  const [deleteFileName, setDeleteFileName] = useState("");
  const [deletedBy, setDeletedBy] = useState("all");
  const [deleteSelectedDate, setDeleteSelectedDate] = useState(
    new Date().toLocaleDateString("fr-CA")
  );
  const [eventFilterStartDate, setEventFilterStartDate] = useState(
    new Date().toLocaleDateString("fr-CA")
  );
  const [eventFilterEndDate, setEventFilterEndDate] = useState(
    new Date().toLocaleDateString("fr-CA")
  );
  const [sendCommand, {}] = useSendCommandMutation();
  const { data: reportData = { idp_client: [], report: {} }, isLoading } =
    useGetIdpsReportQuery(
      {},
      {
        pollingInterval: 3000,
        refetchOnMountOrArgChange: true,
        skip: isCalendarOpen,
      }
    );
  const handleOpenChangeFilter = (newOpen) => {
    setOpenPopfilter(newOpen);
  };
  const handleOpenChangeDelete = (newOpen) => {
    setOpenPopDelete(newOpen);
  };
  const token = theme.useToken().token;
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const panelStyle = {
    marginBottom: 8,
    background: token.colorBgContainer,
    borderRadius: token.borderRadiusSM,
    boxShadow: token?.Card?.boxShadow,
    border: "none",
    alignItem: "center",
  };

  const onPageChange = (page, pageSize) => {
    setCurrent(page);
    setPageSize(pageSize);
  };

  if (isLoading) {
    return (
      <div style={{ textAlign: "center" }}>
        <Spin size="large" />
      </div>
    );
  }

  const handleEventFilterSave = async (item) => {
    try {
      const command = [
        {
          command: `idps records search -f ${eventFilterFileName} -st ${eventFilterStartDate}-00:00 -et ${eventFilterEndDate}-23:59`,
          client: item,
        },
      ];
      await sendCommand(command).unwrap();
    } catch (error) {
      console.log(error);
    } finally {
      setOpenPopfilter(false);
    }
  };

  const handleDeleteSave = async (item) => {
    let command = `idps records delete`;
    if (deletedBy === "byDate")
      command = `idps records delete -d ${deleteSelectedDate}`;
    if (deletedBy === "byDatenfile")
      command = `idps records delete -f ${deleteFileName} -d ${deleteSelectedDate}`;
    try {
      const commands = [
        {
          command,
          client: item,
        },
      ];
      await sendCommand(commands).unwrap();
    } catch (error) {
      console.log(error);
    } finally {
      setOpenPopDelete(false);
    }
  };

  const eventFiltercontent = (item) => (
    <Flex vertical align="center" gap={4}>
      <Divider style={{ margin: "8px 0" }} />

      <Typography.Text style={{ width: "100%" }}>File Name</Typography.Text>
      <Input
        placeholder="File name"
        onChange={(e) => setEventFilterFileName(e.target.value)}
      />
      <Divider style={{ margin: "8px 0" }} />

      <Typography.Text style={{ width: "100%" }}>Date Range</Typography.Text>

      <RangePicker
        id={{
          start: "startInput",
          end: "endInput",
        }}
        onChange={(date, dateString) => {
          setEventFilterStartDate(dateString[0]);
          setEventFilterEndDate(dateString[1]);
        }}
        allowClear={false}
        disabledDate={(currentDate) => {
          return currentDate && currentDate > dayjs().endOf("day");
        }}
      />
      <Divider style={{ margin: "8px 0" }} />

      <Button
        type="primary"
        onClick={() => handleEventFilterSave(item)}
        block
        disabled={!eventFilterFileName}
      >
        Save
      </Button>
    </Flex>
  );

  const eventDeletecontent = (item) => (
    <Flex vertical align="center" gap={4}>
      <Divider style={{ margin: "8px 0" }} />
      <Radio.Group
        value={deletedBy}
        onChange={(e) => setDeletedBy(e.target.value)}
      >
        <Radio.Button value="all">All</Radio.Button>
        <Radio.Button value="byDate">By Date</Radio.Button>
        <Radio.Button value="byDatenfile">By Date & File</Radio.Button>
      </Radio.Group>
      <Divider style={{ margin: "8px 0" }} />
      {deletedBy === "byDatenfile" && (
        <div>
          <Typography.Text style={{ width: "100%" }}>File Name</Typography.Text>
          <Input
            placeholder="File name"
            onChange={(e) => setEventFilterFileName(e.target.value)}
          />
          <Divider style={{ margin: "8px 0" }} />
        </div>
      )}
      {deletedBy !== "all" && (
        <div style={{ width: "100%" }}>
          <Typography.Text style={{ width: "100%" }}>
            Select Date
          </Typography.Text>
          <DatePicker
            style={{ width: "100%" }}
            value={dayjs(deleteSelectedDate)}
            onChange={(_, dateString) => setDeleteSelectedDate(dateString)}
            allowClear={false}
            onFocus={(e) => {
              setIsCalendarOpen(true);
            }}
            disabledDate={(currentDate) => {
              return currentDate && currentDate > dayjs().endOf("day");
            }}
          />
        </div>
      )}
      <Button type="primary" onClick={() => handleDeleteSave(item)} block>
        send
      </Button>
    </Flex>
  );

  const items = reportData?.idp_client
    .slice((current - 1) * pageSize, (current - 1) * pageSize + pageSize)
    .map((item) => ({
      key: item,
      label: (
        <Space size="large">
          <Typography.Title type="secondary" level={5} style={{ margin: 0 }}>
            Service name: {item}
          </Typography.Title>
          <Typography.Title type="secondary" level={5} style={{ margin: 0 }}>
            start at:{" "}
            {dayjs(reportData?.report[item].start_time).format(
              "YYYY/MM/DD HH:mm:ss"
            )}
          </Typography.Title>
        </Space>
      ),
      children: (
        <Row gutter={[4, 4]}>
          <Col span={24}>
            <RcResizeObserver
              key="resize-observer"
              onResize={(offset) => {
                setResponsive(offset.width < 767);
              }}
            >
              <StatisticCard.Group direction={responsive ? "column" : "row"}>
                <StatisticCard
                  bodyStyle={{ paddingInline: 5, paddingBlock: 5 }}
                  chart={
                    <CategoriesTable
                      data={reportData?.report[item].rules}
                      id={item}
                      onOkClick={(name) => setSelectedRulename(name)}
                    />
                  }
                  colSpan={responsive ? 24 : 16}
                />
                <StatisticCard
                  bodyStyle={{ paddingInline: 5, paddingBlock: 5 }}
                  title="Packets count by rules"
                  colSpan={responsive ? 24 : 8}
                  chart={
                    <IdpsPieChart
                      data={reportData?.report[item].rulepackets.filter(
                        (item) => item.name === selectedRulename
                      )}
                    />
                  }
                />
              </StatisticCard.Group>
            </RcResizeObserver>
          </Col>

          <Col span={24}>
            <Divider style={{ margin: 0 }} />
            <RcResizeObserver
              key="resize-observer"
              onResize={(offset) => {
                setResponsive(offset.width < 767);
              }}
            >
              <StatisticCard.Group direction={responsive ? "column" : "row"}>
                <StatisticCard
                  bodyStyle={{
                    paddingInline: 5,
                    paddingBlock: 5,
                  }}
                  headStyle={{ paddingInline: 5 }}
                  title="Record list"
                  chart={
                    <IdpsColumnChart
                      data={
                        reportData?.report[item].recordlist.filter(
                          (item) => item.date === selectedRecordDate
                        )[0]?.files || []
                      }
                      id={item}
                      selectedDate={selectedRecordDate}
                      onOkClick={(date) => {
                        dispatch(setSelectedRecordDate(date));
                        setIsCalendarOpen(false);
                      }}
                      onFocusClick={(e) => {
                        setIsCalendarOpen(true);
                      }}
                    />
                  }
                  colSpan={responsive ? 24 : 8}
                />
                <StatisticCard
                  bodyStyle={{
                    paddingInline: 5,
                    paddingBlock: 5,
                  }}
                  headStyle={{ paddingInline: 5 }}
                  title="Events list"
                  colSpan={responsive ? 24 : 16}
                  chart={
                    <IdpsEventTable data={reportData?.report[item]} id={item} />
                  }
                  extra={
                    <Space>
                      <Popover
                        placement="left"
                        title="Delete Record"
                        content={() => eventDeletecontent(item)}
                        trigger="click"
                        showArrow={true}
                        open={openPopdelete}
                        onOpenChange={handleOpenChangeDelete}
                      >
                        <Button
                          type="primary"
                          aria-label="delete-idp-event"
                          icon={<DeleteOutlined />}
                        />
                      </Popover>
                      <Popover
                        placement="left"
                        title="Event filter"
                        content={() => eventFiltercontent(item)}
                        trigger="click"
                        showArrow={true}
                        open={openPopfilter}
                        onOpenChange={handleOpenChangeFilter}
                      >
                        <Button
                          type="primary"
                          aria-label="filter-idp-event"
                          icon={<FilterOutlined />}
                        />
                      </Popover>
                    </Space>
                  }
                />
              </StatisticCard.Group>
            </RcResizeObserver>
          </Col>
        </Row>
      ),
      style: panelStyle,
    }));

  const handleOnchageCollapse = () => {
    setSelectedRulename("");
    dispatch(setSelectedRecordDate(new Date().toLocaleDateString("fr-CA")));
  };

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={24} lg={18}>
        <Card
          title="IDPS Report"
          bodyStyle={{ padding: "5px" }}
          extra={<Space></Space>}
          actions={[
            <Pagination
              showSizeChanger
              current={current}
              defaultCurrent={1}
              onChange={onPageChange}
              pageSize={pageSize}
              pageSizeOptions={[5, 10]}
              total={reportData?.idp_client?.length}
            />,
          ]}
        >
          {reportData.idp_client.length > 0 ? (
            <Collapse
              accordion
              items={items}
              force
              className="custom-colapse"
              onChange={handleOnchageCollapse}
            />
          ) : (
            <Result
              icon={<InboxOutlined />}
              title="No IDPS services found, Please run IDPS services"
            />
          )}
        </Card>
      </Col>
      <Col xs={24} md={24} lg={6}>
        <EventListCard />
      </Col>
    </Row>
  );
};

export default IdpsPage;
