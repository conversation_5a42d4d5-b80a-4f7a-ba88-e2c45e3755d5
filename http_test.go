package mnms

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"

	jwt "github.com/dgrijalva/jwt-go"
	"github.com/go-chi/jwtauth/v5"
	"github.com/pquerna/otp/totp"
	"github.com/qeof/q"
)

// to test https
// 1. start mnms `./mnmsctl/mnmsctl -s`
// 2. check out IP address of the machine `curl ipconfig.io`
// 3. start reverse proxy `sudo caddy reverse-proxy --from ***************.sslip.io --to https://localhost:27182`

// Testing jwt with curl
// curl -H 'Accept: application/json' -H "Authorization: Bearer ${TOKEN}" https://{hostname}/api/myresource
// example get token
// curl -X POST -H 'Accept: application/json'  https://localhost:27182/api/v1/login -d '{"user":"admin"}'
// get user password
// curl -H 'Accept: application/json' -H "Authorization: Bearer ${TOKEN}" https://localhost:27182/api/v1/users -d '{"username":"admin"}'

func TestAuthentication(t *testing.T) {
	QC.IsRoot = true
	QC.Name = "root"
	// run services
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		GwdMain()
	}()
	wg.Add(1)
	go func() {
		// root and non-root run http api service.
		// if both root and non-root runs on same machine (should not happen)
		// then whoever runs first will run http service (port conflict)
		defer wg.Done()
		HTTPMain()
		// TODO root to dump snapshots of devices, logs, commands
	}()

	q.Q("wait for root to become ready...")
	if err := waitForRoot(); err != nil {
		t.Fatal(err)
	}

	// http request to localhost:27182/api
	resp, err := http.Get("http://localhost:27182/api")
	if err != nil {
		t.Fatal("mnmsctl is not running, should run mnmsctl first")
	}
	if resp == nil {
		t.Fatal("resp should not be nil")
	}
	// save close, resp should not be nil here
	resp.Body.Close()

	// init
	err = cleanMNMSConfig()
	if err != nil {
		q.Q(err)
	}
	err = InitDefaultMNMSConfigIfNotExist()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		err := cleanMNMSConfig()
		if err != nil {
			t.Error(err)
		}
	}()

	// check default bbrootconfig.enc
	c, err := GetUserConfig("admin")
	if err != nil {
		t.Fatal(err)
	}
	if c.Name != "admin" {
		t.Fatal("default user should be admin")
	}
	if c.Password != AdminDefaultPassword {
		t.Fatal("default password should be default")
	}

	body, err := json.Marshal(map[string]string{
		"user":     "admin",
		"password": AdminDefaultPassword,
	})
	if err != nil {
		t.Fatal(err)
	}
	// make a post request with username = admin and passwrord = adminPass
	res, err := http.Post("http://localhost:27182/api/v1/login", "application/json", bytes.NewBuffer(body))
	if err != nil || res.StatusCode != http.StatusOK {
		resText, _ := io.ReadAll(res.Body)
		t.Log("resText", string(resText))
		t.Fatal("should be able to login with admin and adminPass")
	}

	if res == nil {
		t.Fatal("res should not be nil")
	}

	var recBody map[string]interface{}
	err = json.NewDecoder(res.Body).Decode(&recBody)
	if err != nil {
		t.Fatal(err)
	}
	// save close, res should not be nil here
	res.Body.Close()
	t.Log("/login response: ", recBody)
	token, ok := recBody["token"].(string)
	if !ok {
		t.Fatal("token is not string", token)
	}
}

// TestJWT tests JWT
func TestJWT(t *testing.T) {
	QC.Name = "test"

	// reset config
	err := cleanMNMSConfig()
	if err != nil {
		q.Q(err)
	}
	err = InitDefaultMNMSConfigIfNotExist()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		err := cleanMNMSConfig()
		if err != nil {
			t.Error(err)
		}
	}()

	// sample token string taken from the New example
	tokenString, err := generateJWT("admin", AdminDefaultPassword)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(tokenString)

	claims, err := parseJWT(tokenString)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(claims)

	// create a invalied token
	privateKey, _ := ecdsa.GenerateKey(elliptic.P521(), rand.Reader)
	token := jwt.NewWithClaims(jwt.SigningMethodES512, jwt.MapClaims(claims))
	tokenString, err = token.SignedString(privateKey)
	if err != nil {
		t.Fatal(err)
	}
	_, err = parseJWT(tokenString)
	if err == nil {
		t.Fatal("should be error")
	}
}

// TestFileServer tests file server
func TestFileServer(t *testing.T) {
	QC.IsRoot = true
	QC.Name = "root"
	// run services
	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		// root and non-root run http api service.
		// if both root and non-root runs on same machine (should not happen)
		// then whoever runs first will run http service (port conflict)
		defer wg.Done()
		HTTPMain()
		// TODO root to dump snapshots of devices, logs, commands
	}()

	q.Q("wait for root to become ready...")
	if err := waitForRoot(); err != nil {
		t.Fatal(err)
	}

	// create a file for testing
	fileName := "test.txt"
	fileDir, err := EnsureStaticFilesFolderExist()
	if err != nil {
		t.Fatal("check static files folder fail", err)
	}
	filePath := filepath.Join(fileDir, fileName)
	// fill file with some content to test file server
	// write 3k rand data
	data := make([]byte, 3*1024)
	if _, err := io.ReadFull(rand.Reader, data); err != nil {
		t.Fatal("generate rand data fail", err)
	}
	// write data to filePath
	err = os.WriteFile(filePath, data, 0o644)
	if err != nil {
		t.Fatal("write file fail", err)
	}

	// GetWithToken can pass
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("get token fail", err)
	}
	res, err := GetWithToken("http://localhost:27182/api/v1/files/test.txt", admintoken)
	if err != nil || res.StatusCode != http.StatusOK {
		t.Fatal("should be able to get /api/v1/devices with token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	}
	// check content
	content, err := io.ReadAll(res.Body)
	if err != nil {
		t.Fatal("read body fail", err)
	}
	if len(content) == 0 {
		t.Fatal("content should not be empty")
	}
	// compare with data
	if !bytes.Equal(content, data) {
		t.Fatal("content should be equal to data")
	}
	// save close, res should not be nil here
	res.Body.Close()

	// Check without token should fail
	res, err = http.Get("http://localhost:27182/api/v1/files/test.txt")
	if err != nil || res.StatusCode != http.StatusUnauthorized {
		t.Fatal("should not be able to get /api/v1/devices without token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	}
	res.Body.Close()

	// delete file
	err = os.Remove(filePath)
	if err != nil {
		t.Fatal("delete file fail", err)
	}
}

func TestHandleValidate2FA(t *testing.T) {
	killNmsctlProcesses()

	QC.IsRoot = true
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		HTTPMain()
	}()

	// reset config
	err := cleanMNMSConfig()
	if err != nil {
		q.Q(err)
	}
	err = InitDefaultMNMSConfigIfNotExist()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		err := cleanMNMSConfig()
		if err != nil {
			t.Error(err)
		}
	}()

	AddUserConfig("john", "user", "John@1234", "<EMAIL>")

	c, err := GetUserConfig("john")
	if err != nil {
		t.Errorf("Error not expected but got %v", err)
	}

	c.Enable2FA = true

	secret, err := generate2FASecret(c.Email)
	if err != nil {
		t.Errorf("Error not expected but got %v", err)
	}
	c.Secret = secret
	err = MergeUserConfig(*c)
	if err != nil {
		t.Errorf("Error not expected but got %v", err)
	}

	s, err := totp.GenerateCode(secret, time.Now())
	if err != nil {
		t.Errorf("Error not expected but got %v", err)
	}

	sessionID := createLoginSession(*c)

	body, err := json.Marshal(map[string]string{
		"sessionID": sessionID,
		"code":      s,
	})
	if err != nil {
		t.Fatal(err)
	}
	// make a post request with sessionID code
	res, err := http.Post("http://localhost:27182/api/v1/2fa/validate", "application/json", bytes.NewBuffer(body))
	if err != nil || res.StatusCode != http.StatusOK {
		resText, _ := io.ReadAll(res.Body)
		t.Log("resText", string(resText))
		t.Fatal("should be able to validate with sessionID and code")
	}
}

func TestHandle2FA(t *testing.T) {
	killNmsctlProcesses()

	QC.IsRoot = true
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		HTTPMain()
	}()

	// reset config
	err := cleanMNMSConfig()
	if err != nil {
		q.Q(err)
	}
	err = InitDefaultMNMSConfigIfNotExist()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		err := cleanMNMSConfig()
		if err != nil {
			t.Error(err)
		}
	}()

	AddUserConfig("root", "user", "Root@1234", "<EMAIL>")

	c, err := GetUserConfig("root")
	if err != nil {
		t.Errorf("Error not expected but got %v", err)
	}

	c.Enable2FA = true

	secret, err := generate2FASecret(c.Email)
	if err != nil {
		t.Errorf("Error not expected but got %v", err)
	}
	c.Secret = secret
	err = MergeUserConfig(*c)
	if err != nil {
		t.Errorf("Error not expected but got %v", err)
	}

	// Make a Get request without Token should fail
	res, err := http.Get("http://localhost:27182/api/v1/2fa/secret?user=root")
	if err != nil || res.StatusCode != http.StatusUnauthorized {
		t.Fatal("should not be able to get /api/v1/2fa/secret?user=root without token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	}
	// save close, res should not be nil here
	res.Body.Close()

	// GetWithToken can pass
	admintoken, err := GetToken("root")
	if err != nil {
		t.Fatal("get token fail", err)
	}

	res, err = GetWithToken("http://localhost:27182/api/v1/2fa/secret?user=root", admintoken)
	if err != nil || res.StatusCode != http.StatusOK {
		t.Fatal("should be able to get /api/v1/2fa/secret?user=root with token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	}
	// save close, res should not be nil here
	res.Body.Close()

	body, err := json.Marshal(map[string]string{
		"user": c.Name,
	})
	if err != nil {
		t.Fatal(err)
	}
	// make a POST request with user
	res1, err1 := http.Post("http://localhost:27182/api/v1/2fa/secret", "application/json", bytes.NewBuffer(body))
	if err1 != nil || res.StatusCode != http.StatusOK {
		resText, _ := io.ReadAll(res1.Body)
		t.Log("resText", string(resText))
		t.Fatal("should be able to validate with user")
	}

	// make a PUT request with user
	req, err := http.NewRequest("PUT", "http://localhost:27182/api/v1/2fa/secret", bytes.NewBuffer(body))
	if err != nil {
		t.Fatal("create request fail", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatal("Error not expected but got", err)
	}
	defer resp.Body.Close()

	// make a DELETE request with user
	req1, err1 := http.NewRequest("DELETE", "http://localhost:27182/api/v1/2fa/secret", bytes.NewBuffer(body))
	if err1 != nil {
		t.Fatal("create request fail", err)
	}

	req.Header.Set("Content-Type", "application/json")

	clients := &http.Client{}
	resp1, err1 := clients.Do(req1)
	if err1 != nil {
		t.Fatal("Error not expected but got", err)
	}
	defer resp1.Body.Close()
}

func TestHandleUsers(t *testing.T) {
	killNmsctlProcesses()

	QC.IsRoot = true
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		HTTPMain()
	}()

	// reset config
	err := cleanMNMSConfig()
	if err != nil {
		q.Q(err)
	}
	err = InitDefaultMNMSConfigIfNotExist()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		err := cleanMNMSConfig()
		if err != nil {
			t.Error(err)
		}
	}()

	res, err := http.Get("http://localhost:27182/api/v1/users")
	if err != nil || res.StatusCode != http.StatusUnauthorized {
		t.Fatal("should not be able to get /api/v1/users without token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	}
	// save close, res should not be nil here
	res.Body.Close()

	// GetWithToken can pass
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("get token fail", err)
	}

	res, err = GetWithToken("http://localhost:27182/api/v1/users", admintoken)
	if err != nil || res.StatusCode != http.StatusOK {
		t.Fatal("should be able to get /api/v1/users with token")
	}

	if res == nil {
		t.Fatal("res should not be nil")
	}
	// save close, res should not be nil here
	res.Body.Close()
}

func TestHandleLogs(t *testing.T) {
	QC.IsRoot = true
	go func() {
		HTTPMain()
	}()

	// post logs should fail
	body, err := json.Marshal(map[string]Log{
		"log1": {
			Kind:     "error",
			Messages: []string{"Error message 1", "Error message 2"},
		},

		"log2": {
			Kind:     "info",
			Messages: []string{"Info message 1", "Info message 2"},
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	q.Q(string(body))

	resp, err := http.Post("http://localhost:27182/api/v1/logs", "application/text", bytes.NewBuffer([]byte(body)))
	if err != nil {
		t.Fatalf("An error occurred while making a POST request: %v", err)
	}
	if resp == nil {
		t.Fatal("resp should not be nil")
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}
	if resp.StatusCode != http.StatusUnauthorized {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post logs should fail err: %v code: %d", err, resp.StatusCode)
	}
	q.Q(resp.Header)

	// GetWithToken can pass
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("get token fail", err)
	}

	// PostWithToken should pass
	resp, err = PostWithToken("http://localhost:27182/api/v1/logs", admintoken, bytes.NewBuffer([]byte(body)))
	if resp == nil {
		t.Fatal("resp should not be nil")
	}
	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post logs should pass err: %v code: %d", err, resp.StatusCode)
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}
	q.Q(resp.Header)

	// Try to get /api/v1/logs without token should fail
	res, err := http.Get("http://localhost:27182/api/v1/logs")
	if res == nil {
		t.Fatal("res should not be nil")
	}
	if err != nil || res.StatusCode != http.StatusUnauthorized {
		t.Fatal("should not be able to get /api/v1/logs without token")
	} else {
		// save close, res should not be nil here
		defer res.Body.Close()
	}

	// GetWithToken can pass
	res, err = GetWithToken("http://localhost:27182/api/v1/logs", admintoken)
	if err != nil || res.StatusCode != http.StatusOK {
		t.Fatal("should be able to get /api/v1/logs with token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	} else {
		// save close, res should not be nil here
		defer res.Body.Close()
	}
}

func TestHandleTopology(t *testing.T) {
	// Initialize config for test
	QC.IsRoot = true

	// reset config
	err := cleanMNMSConfig()
	if err != nil {
		q.Q(err)
	}
	err = InitDefaultMNMSConfigIfNotExist()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		err := cleanMNMSConfig()
		if err != nil {
			t.Error(err)
		}
	}()

	// Create test server with your HTTP router
	server := httptest.NewServer(BuildRouter())
	defer server.Close()

	// preinsert device info
	b := InsertDev(DevInfo{
		Mac:       "00:00:00:a1:b2:c3",
		IPAddress: "***********",
		ModelName: "model1",
		ScannedBy: "demoServices",
		Scanproto: "gwd",
	})
	if !b {
		t.Fatal("preinsert device info failed")
	}

	// Test topology data
	testTopology := Topology{
		Id:        "00:00:00:a1:b2:c3",
		IpAddress: "***********",
		ModelName: "model1",
		Services:  "demoServices",
		LinkData: []Link{
			{
				Source:      "node1",
				Target:      "node2",
				SourcePort:  "port1",
				TargetPort:  "port2",
				Edge:        "edge1",
				BlockedPort: "false",
			},
		},
	}

	body, err := json.Marshal(testTopology)
	if err != nil {
		t.Fatal(err)
	}

	// Test 1: POST topology without token should fail
	resp, err := http.Post(server.URL+"/api/v1/topology", "application/json", bytes.NewBuffer(body))
	if err != nil {
		t.Fatal("post request failed:", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusUnauthorized {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post topology should fail, expected %d but got %d", http.StatusUnauthorized, resp.StatusCode)
	}

	// Get admin token for authenticated requests
	admintoken, err := generateJWT("admin", AdminDefaultPassword)
	if err != nil {
		t.Fatal("get token fail", err)
	}

	// Test 2: POST topology with token should pass
	req, err := http.NewRequest("POST", server.URL+"/api/v1/topology", bytes.NewBuffer(body))
	if err != nil {
		t.Fatal("create request failed:", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+admintoken)

	client := &http.Client{}
	resp, err = client.Do(req)
	if err != nil {
		t.Fatal("post with token failed:", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post topology should pass, expected %d but got %d", http.StatusOK, resp.StatusCode)
	}

	// Test 3: GET topology without token should fail
	res, err := http.Get(server.URL + "/api/v1/topology")
	if err != nil {
		t.Fatal("get request failed:", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusUnauthorized {
		t.Errorf("should not be able to get /api/v1/topology without token, got status %d", res.StatusCode)
	}

	// Test 4: GET topology with token should pass
	req, err = http.NewRequest("GET", server.URL+"/api/v1/topology", nil)
	if err != nil {
		t.Fatal("create get request failed:", err)
	}
	req.Header.Set("Authorization", "Bearer "+admintoken)

	res, err = client.Do(req)
	if err != nil {
		t.Fatal("get with token failed:", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		t.Errorf("should be able to get /api/v1/topology with token, got status %d", res.StatusCode)
	}
}

func TestHandleInfo(t *testing.T) {
	QC.IsRoot = true
	go func() {
		HTTPMain()
	}()

	// Try to get /api/v1/info without token should fail
	res, err := http.Get("http://localhost:27182/api/v1/info")
	if err != nil || res.StatusCode != http.StatusUnauthorized {
		t.Fatal("should not be able to get /api/v1/info without token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	}
	// save close, res should not be nil here
	res.Body.Close()

	// GetWithToken can pass
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("get token fail", err)
	}
	res, err = GetWithToken("http://localhost:27182/api/v1/info", admintoken)
	if err != nil || res.StatusCode != http.StatusOK {
		t.Fatal("should be able to get /api/v1/info with token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	}
	// save close, res should not be nil here
	res.Body.Close()
}

func TestHandleDevices(t *testing.T) {
	QC.IsRoot = true
	go func() {
		HTTPMain()
	}()

	// post devices should fail
	body1, err := json.Marshal(map[string]DevInfo{
		"devInfo": {
			Mac:            "00-60-E9-1B-A9-0A",
			ModelName:      "EHG7508",
			Timestamp:      "1683529895",
			Scanproto:      "gwd",
			IPAddress:      "**********",
			Netmask:        "***********",
			Gateway:        "0.0.0.0",
			Hostname:       "switch1",
			Kernel:         "7.70",
			Ap:             "EHG7508 Application: V7.70-svn3249",
			ScannedBy:      "client",
			ArpMissed:      0,
			Lock:           false,
			ReadCommunity:  "read",
			WriteCommunity: "write",
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	resp, err := http.Post("http://localhost:27182/api/v1/devices", "application/text", bytes.NewBuffer([]byte(body1)))
	if err != nil {
		t.Fatalf("An error occurred while making a POST request: %v", err)
	}
	if resp == nil {
		t.Fatal("resp should not be nil")
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}
	if resp.StatusCode != http.StatusUnauthorized {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post devices should fail err: %v code: %d", err, resp.StatusCode)
	}
	q.Q(resp.Header)

	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("get token fail", err)
	}

	// PostWithToken should pass
	resp, err = PostWithToken("http://localhost:27182/api/v1/devices", admintoken, bytes.NewBuffer([]byte(body1)))
	if resp == nil {
		t.Fatal("resp should not be nil")
	}
	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post devices should pass err: %v code: %d", err, resp.StatusCode)
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}
	q.Q(resp.Header)

	// Try to get /api/v1/devices without token should fail
	res, err := http.Get("http://localhost:27182/api/v1/devices")
	if err != nil || res.StatusCode != http.StatusUnauthorized {
		t.Fatal("should not be able to get /api/v1/devices without token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}

	// GetWithToken can pass
	res, err = GetWithToken("http://localhost:27182/api/v1/devices", admintoken)
	if err != nil || res.StatusCode != http.StatusOK {
		t.Fatal("should be able to get /api/v1/devices with token")
	}
	if res == nil {
		t.Fatal("res should not be nil")
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}
}

func TestHandleAddUsers(t *testing.T) {
	killNmsctlProcesses()

	QC.IsRoot = true
	go func() {
		HTTPMain()
	}()

	// reset config
	err := cleanMNMSConfig()
	if err != nil {
		q.Q(err)
	}
	err = InitDefaultMNMSConfigIfNotExist()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		err := cleanMNMSConfig()
		if err != nil {
			t.Error(err)
		}
	}()

	body, err := json.Marshal(map[string]string{
		"name":     "testroot",
		"email":    "<EMAIL>",
		"password": "Testroot@123",
		"role":     "user",
	})
	if err != nil {
		t.Fatal(err)
	}

	// Post request without token should fail
	resp, err := http.Post("http://localhost:27182/api/v1/users", "application/text", bytes.NewBuffer([]byte(body)))
	if err != nil {
		t.Fatalf("An error occurred while making a POST request: %v", err)
	}
	if resp == nil {
		t.Fatal("resp should not be nil")
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}
	if resp.StatusCode != http.StatusUnauthorized {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post users should fail err: %v code: %d", err, resp.StatusCode)
	}
	q.Q(resp.Header)

	// Post request with admin token should pass
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("get token fail", err)
	}

	resp, err = PostWithToken("http://localhost:27182/api/v1/users", admintoken, bytes.NewBuffer([]byte(body)))
	if resp == nil {
		t.Fatal("resp should not be nil")
	}
	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post users should pass err: %v code: %d", err, resp.StatusCode)
	}
	// save close, resp should not be nil here
	resp.Body.Close()
}

func TestHandleCommands(t *testing.T) {
	QC.IsRoot = true
	go func() {
		HTTPMain()
	}()

	cmdJson := `[{"command":"scan gwd"}]`

	resp, error := http.Post("http://localhost:27182/api/v1/commands", "application/text", bytes.NewBuffer([]byte(cmdJson)))

	if resp == nil {
		t.Fatal("resp should not be nil")
	}

	if resp.StatusCode != http.StatusUnauthorized {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Fatalf("post command should fail err: %v code: %d", error, resp.StatusCode)
	}

	// save close, resp should not be nil here
	resp.Body.Close()

	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("get token fail", err)
	}

	// PostWithToken should pass
	resp, err = PostWithToken("http://localhost:27182/api/v1/commands", admintoken, bytes.NewBuffer([]byte(cmdJson)))

	if resp == nil {
		t.Fatal("resp should not be nil")
	}

	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Fatalf("post command should pass err: %v code: %d", err, resp.StatusCode)
	}
	// save close, resp should not be nil here
	resp.Body.Close()

	// Try to get /api/v1/commands without token should fail
	res, err := http.Get("http://localhost:27182/api/v1/commands")

	if err != nil || res.StatusCode != http.StatusUnauthorized {
		t.Fatal("should not be able to get /api/v1/commands without token")
	}

	if res == nil {
		t.Fatal("res should not be nil")
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}

	// GetWithToken can pass
	res, err = GetWithToken("http://localhost:27182/api/v1/commands", admintoken)

	if err != nil || res.StatusCode != http.StatusOK {
		t.Fatal("should be able to get /api/v1/commands with token")
	}

	if res == nil {
		t.Fatal("res should not be nil")
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}
}

/*
func TestHandleCheckOfflineEnable(t *testing.T) {
	// Start the HTTP server in a goroutine
	go HTTPMain()

	// Test: POST without token should fail
	reqBody, err := json.Marshal(map[string]interface{}{"enable": true})
	if err != nil {
		t.Fatal(err)
	}

	resp, err := http.Post("http://localhost:27182/api/v1/check-offline/enable", "application/json", bytes.NewBuffer(reqBody))
	if err != nil || resp.StatusCode != http.StatusUnauthorized {
		t.Fatalf("POST request should fail. Error: %v, Status code: %d", err, resp.StatusCode)
	}
	defer resp.Body.Close()

	// Obtain admin token
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("Failed to get admin token", err)
	}

	// Test: PostWithToken should pass
	resp, err = PostWithToken("http://localhost:27182/api/v1/check-offline/enable", admintoken, bytes.NewBuffer(reqBody))
	if err != nil || resp.StatusCode != http.StatusOK {
		t.Fatalf("POST request should pass. Error: %v, Status code: %d", err, resp.StatusCode)
	}
	defer resp.Body.Close()

	// Test: GET without token should fail
	res, err := http.Get("http://localhost:27182/api/v1/check-offline/enable")
	if err != nil || res.StatusCode != http.StatusUnauthorized {
		t.Fatal("Should not be able to GET without token")
	}
	defer res.Body.Close()

	// Test: GetWithToken can pass
	res, err = GetWithToken("http://localhost:27182/api/v1/check-offline/enable", admintoken)
	if err != nil || res.StatusCode != http.StatusOK {
		t.Fatal("Should be able to GET with token")
	}
	defer res.Body.Close()
}
*/

func TestHandleClientInfo(t *testing.T) {
	QC.IsRoot = true
	// Start the HTTP server in a goroutine
	go func() {
		HTTPMain()
	}()

	// Step 1: Post a client[] content with multiple clients
	clients := map[string]ClientInfo{
		"nimblclient": {
			Name: "nimblclient", NumDevices: 0, NumCmds: 0, NumLogsReceived: 0, NumLogsSent: 0, Start: 15798959, Now: 157755555, NumGoroutines: 20, IPAddresses: []string{"**********"}, Status: "active",
		},
		"blackbearclient": {
			Name: "blackbearclient", NumDevices: 0, NumCmds: 0, NumLogsReceived: 0, NumLogsSent: 0, Start: 157985868, Now: 151799885, NumGoroutines: 20, IPAddresses: []string{"*************"}, Status: "active",
		},
		"atopclient": {
			Name: "atopclient", NumDevices: 0, NumCmds: 0, NumLogsReceived: 0, NumLogsSent: 0, Start: 148795549, Now: 148795555, NumGoroutines: 20, IPAddresses: []string{"************"}, Status: "inactive",
		},
	}

	req, err := json.Marshal(clients)
	if err != nil {
		t.Fatal(err)
	}

	resp, err := http.Post("http://localhost:27182/api/v1/clients", "application/json", bytes.NewBuffer(req))
	if err != nil {
		t.Fatalf("An error occurred while making a POST request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusUnauthorized {
		t.Errorf("POST request should fail with StatusUnauthorized, got: %v", resp.StatusCode)
	}
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatal("get token fail", err)
	}
	// PostWithToken should pass
	resp, err = PostWithToken("http://localhost:27182/api/v1/clients", admintoken, bytes.NewBuffer(req))
	if resp == nil {
		t.Fatal("resp should not be nil")
	}
	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		t.Logf("msg: %s", msg)
		t.Errorf("post clientinfo should pass err: %v code: %d", err, resp.StatusCode)
	} else {
		// save close, resp should not be nil here
		defer resp.Body.Close()
	}
	q.Q(resp.Header)
	// step 2 : validate post payload with get operation
	// Try to get /api/v1/clients without token should fail
	getResp, err := GetWithToken("http://localhost:27182/api/v1/clients", admintoken)
	if err != nil || getResp.StatusCode != http.StatusOK {
		t.Fatal("Error getting current clients:", err)
	}
	defer getResp.Body.Close()

	// Decode the response body to get the current clients
	var currentClients map[string]ClientInfo
	decoder := json.NewDecoder(getResp.Body)
	if err := decoder.Decode(&currentClients); err != nil {
		t.Fatal("Error decoding GET response body: ", err)
	}

	// Filter out active clients and update information
	updatedClients := make(map[string]ClientInfo)
	for name, client := range currentClients {
		if client.Status == "active" {
			// Add the updated client to the map
			updatedClients[name] = client
		}
	}
	// Step 3: Retrieve active clients, modify, and post the updated content
	// Retrieve the current clients with a GET request
	// Post the updated client information if there are active clients
	if len(updatedClients) > 0 {
		reqUpdated, err := json.Marshal(updatedClients)
		if err != nil {
			t.Fatal("Error marshaling updated clients: ", err)
		}

		// PostWithToken should pass
		resp, err = PostWithToken("http://localhost:27182/api/v1/clients", admintoken, bytes.NewBuffer(reqUpdated))
		if resp == nil {
			t.Fatal("resp should not be nil")
		}
		if resp.StatusCode != http.StatusOK {
			msg, _ := io.ReadAll(resp.Body)
			t.Logf("msg: %s", msg)
			t.Errorf("post updated clientinfo should pass err: %v code: %d", err, resp.StatusCode)
		} else {
			// save close, resp should not be nil here
			defer resp.Body.Close()
		}
		q.Q(resp.Header)

		// Step 4: Retrieve the posted clients
		// GetWithToken can pass
		res, err := GetWithToken("http://localhost:27182/api/v1/clients", admintoken)
		if err != nil || res.StatusCode != http.StatusOK {
			t.Fatal("Error getting posted clients: ", err)
		}
		defer res.Body.Close()

		// Print the response body for debugging
		body, _ := io.ReadAll(res.Body)
		t.Logf("Response body: %s", body)

		// Decode the response body to get the posted clients
		var postedClients map[string]ClientInfo
		decoder := json.NewDecoder(bytes.NewReader(body))
		if err := decoder.Decode(&postedClients); err != nil {
			t.Fatal("Error decoding GET response body: ", err)
		}

		t.Logf("Posted clients: %v", postedClients)
	} else {
		t.Log("No active clients to update.")
	}
}

func TestJWTRenewAgentToken(t *testing.T) {
	QC.Kind = "nms"
	dummyHandler := func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}

	handler := http.HandlerFunc(dummyHandler)
	// Wrap the handler with JWTRenewAgentToken
	wrappedHandler := jwtauth.Verifier(jwtTokenAuth)(jwtauth.Authenticator(JWTRenewAgentToken(handler)))

	// Test case: Request without token
	t.Run("NoToken", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/", nil)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}

		rr := httptest.NewRecorder()
		wrappedHandler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusUnauthorized {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusUnauthorized)
		}
	})

	// Test case: Request with normal token
	t.Run("WithValidToken", func(t *testing.T) {
		// Create a token
		tokenString, err := GetToken("admin")
		if err != nil {
			t.Fatal("get token fail", err)
		}

		req, err := http.NewRequest("GET", "/", nil)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}

		// Add the token to the request
		req.Header.Set("Authorization", "Bearer "+tokenString)

		rr := httptest.NewRecorder()
		wrappedHandler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
		}

		expected := "OK"
		if rr.Body.String() != expected {
			t.Errorf("handler returned unexpected body: got %v want %v", rr.Body.String(), expected)
		}
	})

	// Test case: Request with expired token
	t.Run("WithExpiredToken", func(t *testing.T) {
		// token less than 1 day
		_, token, err := jwtTokenAuth.Encode(map[string]any{
			"user":      "agent",
			"timestamp": time.Now().Format(time.RFC3339),
			"exp":       time.Now().Add(time.Hour * 23).Unix(),
		})
		if err != nil {
			t.Fatal("encode token fail", err)
		}

		req, err := http.NewRequest("GET", "/?prefix=agent&devid=123", nil)
		if err != nil {
			t.Fatalf("could not create request: %v", err)
		}

		// Add the token to the request
		req.Header.Set("Authorization", "Bearer "+token)

		rr := httptest.NewRecorder()
		wrappedHandler.ServeHTTP(rr, req)

		// will return ok
		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
		}

		// check QC.CmdData to see if command exists
		cmd := "agent token refresh 123"
		// map key is the command
		if _, ok := QC.CmdData[cmd]; !ok {
			t.Fatalf("command %s should exist", cmd)
		}
	})
}

func TestNestedNMS(t *testing.T) {

	t.Run("Test Registering Nested NMS", func(t *testing.T) {
		// if self is nms1
		QC.Name = "nms1"
		QC.Kind = "nms"

		// fake NMS info
		nms2 := ClientInfo{
			Name:        "nms2",
			Kind:        "nms",
			NumDevices:  0,
			Start:       int(time.Now().Unix()),
			Now:         int(time.Now().Unix()),
			IPAddresses: []string{"*******"},
		}

		jsonReqBody, _ := json.Marshal(nms2)
		req, err := http.NewRequest("POST", "/api/v1/register", bytes.NewBuffer(jsonReqBody))
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(HandleRegister)
		handler.ServeHTTP(rr, req)

		// should fail due to name should be "nms1/nms2"
		if status := rr.Code; status != http.StatusInternalServerError {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusInternalServerError)
		}

		// register again with correct name
		nms2.Name = "nms1/nms2"
		jsonReqBody, _ = json.Marshal(nms2)
		req, err = http.NewRequest("POST", "/api/v1/register", bytes.NewBuffer(jsonReqBody))
		if err != nil {
			t.Fatal(err)
		}

		rr = httptest.NewRecorder()
		handler = http.HandlerFunc(HandleRegister)
		handler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
		}

		// register log2 to nms1
		log2 := ClientInfo{
			Name:        "log2",
			Kind:        "syslog",
			NumDevices:  0,
			Start:       int(time.Now().Unix()),
			Now:         int(time.Now().Unix()),
			IPAddresses: []string{"*******"},
		}

		jsonReqBody, _ = json.Marshal(log2)
		req, err = http.NewRequest("POST", "/api/v1/register", bytes.NewBuffer(jsonReqBody))
		if err != nil {
			t.Fatal(err)
		}

		rr = httptest.NewRecorder()
		handler = http.HandlerFunc(HandleRegister)
		handler.ServeHTTP(rr, req)

		// log2 should be internal server error
		if status := rr.Code; status != http.StatusInternalServerError {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusInternalServerError)
		}
	})

	t.Run("Test CheckCmds", func(t *testing.T) {
		// fake command at root with client nms2
		cmd := "test command"
		ci := CmdInfo{
			Timestamp:   time.Now().Format(time.RFC3339),
			Command:     cmd,
			NoOverwrite: false,
			All:         false,
			NoSyslog:    false,
			Kind:        "",
			Client:      "nms1/nms2",
			Tag:         "",
			DevId:       "",
		}
		QC.CmdData[cmd] = ci

		// nms1 get commands
		QC.Name = "nms1"
		queryParams := "?id=nms1&id=nms1/nms2&kind=nms"
		req, err := http.NewRequest("GET", "/api/v1/commands"+queryParams, nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		handler := http.HandlerFunc(HandleCommands)
		handler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
		}

		// check if the command is got ( simulate CheckCmds() )
		var cmds map[string]CmdInfo
		decoder := json.NewDecoder(rr.Body)
		if err := decoder.Decode(&cmds); err != nil {
			t.Fatal("Error decoding GET response body: ", err)
		}

		if cmdfake, ok := cmds[cmd]; !ok {
			t.Fatal("command should be got")
		} else {
			// run cmd, suppose to do nothing which status is still ""
			ret := RunCmd(&cmdfake)
			if ret.Status != "" {
				t.Fatal("cmd should not be run")
			}
		}

		// nms2 get commands
		QC.Name = "nms1/nms2"
		queryParams = "?id=nms1/nms2&kind=nms"
		req, err = http.NewRequest("GET", "/api/v1/commands"+queryParams, nil)
		if err != nil {
			t.Fatal(err)
		}

		rr = httptest.NewRecorder()
		handler = http.HandlerFunc(HandleCommands)
		handler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
		}

		// check if the command is got ( simulate CheckCmds() )
		decoder = json.NewDecoder(rr.Body)
		if err := decoder.Decode(&cmds); err != nil {
			t.Fatal("Error decoding GET response body: ", err)
		}

		var retcmd *CmdInfo
		if cmdfake, ok := cmds[cmd]; !ok {
			t.Fatal("command should be got")
		} else {
			// run cmd, suppose to be error
			retcmd = RunCmd(&cmdfake)
			if !strings.Contains(retcmd.Status, "error") {
				t.Fatal("cmd should be run")
			}
		}

		QC.CmdData = make(map[string]CmdInfo)
		temp := *retcmd
		cl := []CmdInfo{temp}
		jsonReqBody, _ := json.Marshal(cl)
		req, err = http.NewRequest("POST", "/api/v1/commands", bytes.NewBuffer(jsonReqBody))
		if err != nil {
			t.Fatal(err)
		}

		rr = httptest.NewRecorder()
		handler = http.HandlerFunc(HandleCommands)
		handler.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusOK {
			t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
		}

		// check if the command is posted
		cmd = retcmd.Command
		if _, ok := QC.CmdData[cmd]; !ok {
			t.Fatal("command should be posted")
		}
	})
}
