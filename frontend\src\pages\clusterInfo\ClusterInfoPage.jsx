import { <PERSON>, <PERSON>, Badge, <PERSON>lex, <PERSON><PERSON>, notification, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { ProTable } from "@ant-design/pro-components";
import {
  clusterInfoSelector,
  RequestClusterInfo,
} from "../../features/clusterInfo/clusterInfoSlice";
import ExportData from "../../components/exportData/ExportData";
import { useTheme } from "antd-style";
import RootClusterInfos from "../../components/clusterInfo/RootClusterInfos";
import { SettingOutlined } from "@ant-design/icons";
import {
  fetchForwardConfig,
  selectForwardConfig,
} from "../../features/forwardConfig/forwardConfigSlice";
import ForwardConfigModal from "../../components/forwardConfig/ForwardConfigModal";
import { useSendCommandMutation } from "../../app/services/commandApi";

const ClusterInfoPage = () => {
  const token = useTheme();
  const { clusterInfoData, fetching } = useSelector(clusterInfoSelector);
  const [exportClusterInfoData, setExportClusterInfoData] = useState([]);
  const [openForwardConfig, setOpenForwardConfig] = useState(false);
  const [selectedForwardService, setSelectedForwardService] = useState("");
  const { data: forwardConfig, loading: forwardLoading } = useSelector(
    selectForwardConfig
  );
  const [sendCommand] = useSendCommandMutation();
  const dispatch = useDispatch();

  // Add handleEditForward function to the component scope
  const handleEditForward = (serviceName) => {
    setSelectedForwardService(serviceName);
    // Refresh data before opening modal
    dispatch(fetchForwardConfig());
    setOpenForwardConfig(true);
  };

  const columns = [
  {
    title: "Service Name",
    dataIndex: "name",
    key: "name",
    width: 250,
    render: (data, record) => {
      return (
        <Flex gap={10} align="center" height="100%">
          {record && record.status === "inactive" ? (
            <Badge status="error" className="cutomBadge" />
          ) : (
            <Badge color="green" className="cutomBadge" status="processing" />
          )}
          {data}
        </Flex>
      );
    },
    sorter: (a, b) => (a.name > b.name ? 1 : -1),
  },
  {
    title: "Devices",
    dataIndex: "num_devices",
    key: "num_devices",
    width: 150,
    sorter: (a, b) => (a.num_devices > b.num_devices ? 1 : -1),
  },
  {
    title: "Cmds",
    width: 100,
    dataIndex: "num_cmds",
    key: "num_cmds",
    sorter: (a, b) => (a.num_cmds > b.num_cmds ? 1 : -1),
  },
  {
    title: "Logs Received",
    dataIndex: "num_logs_received",
    key: "num_logs_received",
    width: 200,
    sorter: (a, b) => (a.num_logs_received > b.num_logs_received ? 1 : -1),
  },
  {
    title: "Logs Sent",
    dataIndex: "num_logs_sent",
    key: "num_logs_sent",
    width: 200,
    sorter: (a, b) => (a.num_logs_sent > b.num_logs_sent ? 1 : -1),
  },
  {
    title: "Start",
    dataIndex: "start",
    key: "start",
    width: 300,
    sorter: (a, b) => (a.start > b.start ? 1 : -1),
    render: (data) => {
      return dayjs(data * 1000).format("YYYY/MM/DD HH:mm:ss");
    },
  },
  {
    title: "Now",
    dataIndex: "now",
    key: "now",
    width: 300,
    render: (data) => {
      return dayjs(data * 1000).format("YYYY/MM/DD HH:mm:ss");
    },
    sorter: (a, b) => (a.Now > b.Now ? 1 : -1),
  },
  {
    title: "IP Addresses",
    dataIndex: "ip_addresses",
    key: "ip_addresses",
    width: 350,    render: (data) => {
      if (Array.isArray(data)) {
        return data?.join();
      }
    },
    sorter: (a, b) => (a.IPAddresses > b.IPAddresses ? 1 : -1),
  },  {
    title: "Actions",
    key: "actions",
    width: 100,
    fixed: "right",
    align: "center",
    render: (_, record) => {
      // Only show edit button for forward services
      if (record.kind === "forward") {
        return (
          <Tooltip title="Configure forwarder">
            <Button
              type="primary"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => handleEditForward(record.name)}
            >
              Config
            </Button>
          </Tooltip>
        );
      }
      return null;
    },
  },
];

  useEffect(() => {
    dispatch(RequestClusterInfo());
  }, []);

  const handleRefreshClick = () => {
    dispatch(RequestClusterInfo());
  };

  useEffect(() => {
    if (clusterInfoData.length > 0) {
      const expClusterInfo = clusterInfoData.map((item) => {
        const formatedStartDate = dayjs(item.start * 1000).format(
          "YYYY/MM/DD HH:mm:ss"
        );
        const formatedNowDate = dayjs(item.now * 1000).format(
          "YYYY/MM/DD HH:mm:ss"
        );
        return {
          ...item,
          start: formatedStartDate,
          now: formatedNowDate,
        };
      });
      setExportClusterInfoData(expClusterInfo);
    }
  }, [clusterInfoData]);

  return (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <RootClusterInfos />
      </Col>
      <Col span={24}>
        <ProTable
          cardProps={{
            style: { boxShadow: token?.Card?.boxShadow },
          }}
          loading={fetching}
          headerTitle="Cluster Info"
          columns={columns}
          dataSource={clusterInfoData}
          rowKey="name"
          pagination={{
            position: ["bottomCenter"],
            showQuickJumper: true,
            size: "default",
            total: clusterInfoData.length,
            defaultPageSize: 10,
            pageSizeOptions: [10, 15, 20, 25],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }}
          scroll={{
            x: 1100,
          }}
          toolbar={{
            actions: [
              <ExportData
                Columns={columns}
                DataSource={exportClusterInfoData}
                title="Cluster_Info"
              />,
            ],
          }}
          options={{
            reload: () => {
              handleRefreshClick();
            },
            fullScreen: false,
            density: false,
            setting: false,
          }}
          search={false}
          dateFormatter="string"
          columnsState={{
            persistenceKey: "clusterinfo-table",
            persistenceType: "localStorage",
          }}        />
      </Col>      <ForwardConfigModal
        visible={openForwardConfig}
        onCancel={() => {
          setOpenForwardConfig(false);
          setSelectedForwardService("");
        }}
        initialConfig={forwardConfig}
        loading={forwardLoading}
        selectedForwardService={selectedForwardService}
        sendCommand={sendCommand}
        onOk={async (values) => {
          // Extract the selected forwarder's configuration
          const forwarderKeys = Object.keys(values);
          if (forwarderKeys.length === 0) {
            notification.error({ message: "No forwarder configuration found" });
            return;
          }

          try {
            // Send command for each forwarder
            for (const forwarderName of forwarderKeys) {
              const forwarderConfig = values[forwarderName];
              
              // Ensure all three services are included
              const configToSend = {
                whatsapp: forwarderConfig.whatsapp || {
                  enabled: false,
                  account_sid: "",
                  auth_token: "",
                  from_number: "",
                  to_numbers: [],
                  alert_config: {
                    min_severity: 0,
                    max_severity: 7,
                    rate_limit_seconds: 300,
                    max_alerts_per_minute: 5,
                    keywords: [],
                    exclude_keywords: [],
                  },
                },
                telegram: forwarderConfig.telegram || {
                  enabled: false,
                  bot_token: "",
                  chat_ids: [],
                  alert_config: {
                    min_severity: 0,
                    max_severity: 7,
                    rate_limit_seconds: 180,
                    max_alerts_per_minute: 10,
                    keywords: [],
                    exclude_keywords: [],
                  },
                },
                mqtt: forwarderConfig.mqtt || {
                  enabled: false,
                  broker_host: "localhost",
                  broker_port: 1883,
                  username: "",
                  password: "",
                  topic: "mnms/alerts",
                  qos: 1,
                  retain: false,
                  alert_config: {
                    min_severity: 0,
                    max_severity: 7,
                    rate_limit_seconds: 60,
                    max_alerts_per_minute: 20,
                    keywords: [],
                    exclude_keywords: [],
                  },
                },
              };

              const jsonString = JSON.stringify(configToSend);
              const command = [
                { 
                  command: `forward raw-json config ${jsonString}`, 
                  client: forwarderName 
                },
              ];
              
              await sendCommand(command).unwrap();
            }
            
            notification.success({ message: "Forward config applied successfully" });
            
            // Refresh the configuration data from API
            dispatch(fetchForwardConfig());
            
            setOpenForwardConfig(false);
            setSelectedForwardService("");
          } catch (error) {
            notification.error({ message: error.data?.error || error.data });
          }
        }}
      />
    </Row>
  );
};

export default ClusterInfoPage;
