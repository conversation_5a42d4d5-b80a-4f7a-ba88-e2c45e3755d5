import {
  createAsyncThunk,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import protectedApis from "../../utils/apis/protectedApis";

export const RequestDeviceSyslogSetting = createAsyncThunk(
  "singleSyslogSetting/RequestDeviceSyslogSetting",
  async (params, thunkAPI) => {
    try {
      const response = await protectedApis.post("/api/v1/commands", [
        {
          command: `config syslog set ${params.mac_address} ${params.logToServer} ${params.serverIP} ${params.serverPort} ${params.logLevel} ${params.logToFlash}`,
        },
      ]);
      const data = await response.data;
      let responseResult = data;
      if (response.status === 200) {
        return responseResult;
      } else {
        return thunkAPI.rejectWithValue("Config syslog device failed !");
      }
    } catch (e) {
      if (e.response && e.response.statusText !== "") {
        return thunkAPI.rejectWithValue(e.response.statusText);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

const singleSyslogSetting = createSlice({
  name: "singleSyslogSetting",
  initialState: {
    visible: false,
    mac_address: "",
    model: "",
    syslogSettingStatus: "in_progress",
    errorSyslogSetting: "",
    resultCommand: [],
    logToFlash: false,
    logLevel: 7,
    logToServer: false,
    serverIP: "",
    serverPort: 514,
  },
  reducers: {
    openSyslogSettingDrawer: (state, { payload }) => {
      const { logToServer, serverIp, serverPort, logLevel, logToFlash } =
        payload.syslogSetting;
      state.visible = true;
      state.mac_address = payload.mac;
      state.model = payload.modelname;
      state.logToFlash = logToFlash === "1";
      state.logLevel = isNaN(Number(logLevel)) ? 7 : Number(logLevel);
      state.logToServer = logToServer === "1";
      state.serverIP = serverIp;
      state.serverPort = isNaN(Number(serverPort)) ? 514 : Number(serverPort);
    },
    closeSyslogSettingDrawer: (state, { payload }) => {
      state.visible = false;
      state.mac_address = "";
      state.model = "";
      state.logToFlash = false;
      state.logLevel = 7;
      state.logToServer = false;
      state.serverIP = "";
      state.serverPort = 514;
    },
    clearSyslogData: (state) => {
      state.syslogSettingStatus = "in_progress";
      state.errorSyslogSetting = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(RequestDeviceSyslogSetting.fulfilled, (state, { payload }) => {
        state.syslogSettingStatus = "success";
        state.errorSyslogSetting = "Config syslog device success !";
        state.resultCommand = Object.keys(payload);
      })
      .addCase(RequestDeviceSyslogSetting.pending, (state, { payload }) => {
        state.syslogSettingStatus = "in_progress";
        state.errorSyslogSetting = "";
        state.resultCommand = [];
      })
      .addCase(RequestDeviceSyslogSetting.rejected, (state, { payload }) => {
        state.syslogSettingStatus = "failed";
        state.errorSyslogSetting = payload;
        state.resultCommand = [];
      });
  },
});

export const {
  openSyslogSettingDrawer,
  closeSyslogSettingDrawer,
  clearSyslogData,
} = singleSyslogSetting.actions;

export const singleSyslogSettingSelector = createSelector(
  (state) => state.singleSyslogSetting,
  ({
    visible,
    mac_address,
    model,
    syslogSettingStatus,
    errorSyslogSetting,
    resultCommand,
    logToFlash,
    logLevel,
    logToServer,
    serverIP,
    serverPort,
  }) => ({
    visible,
    mac_address,
    model,
    syslogSettingStatus,
    errorSyslogSetting,
    resultCommand,
    logToFlash,
    logLevel,
    logToServer,
    serverIP,
    serverPort,
  })
);

export default singleSyslogSetting;
