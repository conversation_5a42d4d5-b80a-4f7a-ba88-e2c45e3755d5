# MCP Tools Documentation

This document provides an overview of the MCP tools implemented in the `mnms/examples/nimbltools/mcp` package for the MNMS (Network Management and Monitoring System) project. These tools enable programmable management of network devices and security rules, supporting both backend automation and user interface operations.

---

## Table of Contents

- [Overview](#overview)
- [Available Tools](#available-tools)
  - [get_device](#get_device)
  - [idps_add_rule](#idps_add_rule)
- [Usage](#usage)

---

## Overview

MCP (Model Context Protocol) tools are Go-based modules that register with the MCP server to provide network management and security automation features. The tools are designed for integration with the MNMS platform, which includes OpenAI API integration for intelligent log analysis and anomaly detection, as well as IDPS (Intrusion Detection and Prevention System) support.

---

## Available Tools

### get_device

**Description:**  
Retrieves all devices managed by the Nimbltools MCP server.

**Parameters:**  
_None_

**Example Call:**  
```json
{
    "tool": "get_device"
}
```

**Return:**  
A JSON/text list of all devices.

---

### idps_add_rule

**Description:**  
Adds a new IDPS rule based on user-provided parameters.

**Parameters:**

| Name            | Required | Description                  | Allowed Values / Example                                     |
| --------------- | -------- | ---------------------------- | ------------------------------------------------------------ |
| client          | Yes      | Service or client name       | e.g., "bbidpsvc"                                             |
| action          | Yes      | Action to take on the packet | "alert", "pass", "drop"                                      |
| sourceip        | No       | Source IP address            | "***********" or "any"                                       |
| sourceport      | No       | Source port                  | "80" or "any"                                                |
| protocol        | Yes      | Network protocol             | "tcp", "udp", "icmp", "http", "http2", "ftp", "tls", "ssl", "smb", "dns", "tftp", "ssh", "smtp", "imap", "modbus", "sip", "dhcp", "snmp", "enip", "nfs", "ntp" |
| destinationip   | No       | Destination IP address       | "********" or "any"                                          |
| destinationport | No       | Destination port             | "443" or "any"                                               |
| msg             | Yes      | Rule description             | "Block suspicious host"                                      |
| sid             | Yes      | Rule SID                     | "100001"                                                     |
| category        | No       | Rule category                | Default: "AI"                                                |

**Example Call:**  
```json
{
    "tool": "idps_add_rule",
    "arguments": {
        "client": "bbidpsvc",
        "action": "alert",
        "sourceip": "***********",
        "sourceport": "any",
        "protocol": "tcp",
        "destinationip": "********",
        "destinationport": "80",
        "msg": "Block suspicious host",
        "sid": "100001",
        "category": "AI"
    }
}
```

**Generated Command:**  
```
idps rules add AI alert tcp *********** any -> ******** 80 (msg:"Block suspicious host";sid:100001;)
```

**Return:**  

- On success: Result string from the backend service.
- On failure: Error message (e.g., missing parameters, timeout).

---

## Usage

1. run nimble .

2. copy below to llm json file

   ```json
   {
     "mcpServers": {
         "nimbltools": {
         "command": "path/nimbltools.exe",
         "args": [""],
         "env": {  
         }
       }
     }
   }
   ```

   

   

---