# Download Remote Atop Website Firmware fiel

Script to download a Atop firmware from a URL and verify its.

## How to work

Download the archive at the URL and verify the length of the archive and whether it can be decompressed.

## Required

- Need to install `curl`, `unzip`.

## Usage

```shell
$ ./downloadRemoteFW.sh -h
Usage: ./downloadRemoteFW.sh -d [Dir] [URL]
  -h       : Display this help messages.
  -d [Dir] : Required. Directory to save the downloaded file.
  [URL]    : Required. The URL of the ZIP file.
```

## How to use

Run command.
```shell
$ ./downloadRemoteFW.sh -d ~/workshop/ https://www.atoponline.com/wp-content/uploads/2017/11/Layer2-K820A820.zip
Fetching file metadata from server...
Expected file size: 19982667 bytes
----------------------------------------
Downloading file from:
https://www.atoponline.com/wp-content/uploads/2017/11/Layer2-K820A820.zip
Saving to: /home/<USER>/workshop/Layer2-K820A820.zip
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed
100 19.0M  100 19.0M    0     0  3903k      0  0:00:04  0:00:04 --:--:-- 4749k
File download complete.
----------------------------------------
Verifying file size...
Expected size: 19982667 bytes
Actual size:   19982667 bytes
File size verification successful.
----------------------------------------
Testing ZIP archive integrity...
No errors detected in compressed data of /home/<USER>/workshop/Layer2-K820A820.zip.
ZIP archive test successful! The file is a valid and uncorrupted ZIP archive.
========================================
All verifications passed. File '/home/<USER>/workshop/Layer2-K820A820.zip' is ready to use!
```