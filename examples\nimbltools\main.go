package main

import (
	"flag"
	"fmt"
	"mnms"
	"mnms/examples/nimbltools/mcp"

	"github.com/mark3labs/mcp-go/server"
)

func main() {
	localRootURL := flag.String("r", fmt.Sprintf("http://localhost:%d", mnms.QC.Port), "address of the root service")
	flag.Parse()

	s := server.NewMCPServer(
		"nimbltools",
		"1.0.0",
		server.WithToolCapabilities(false),
		server.WithRecovery(),
	)
	mcp.NewMcpTool(s, *localRootURL)

	// Start the server
	if err := server.ServeStdio(s); err != nil {
		fmt.Printf("Server error: %v\n", err)
	}

}
