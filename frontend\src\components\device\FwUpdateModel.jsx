import { Form, Input, Modal } from "antd";
import React from "react";

const FwUpdateModel = ({ open, onCancel, onOk, loading }) => {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      width={400}
      forceRender
      maskClosable={false}
      title="Devices Firmware Update"
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_fwUpdate">
        <Form.Item
          name="fwUrl"
          label="F/W Url"
          required
          rules={[
            {
              required: true,
              message: "Please input the f/w url !",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default FwUpdateModel;
