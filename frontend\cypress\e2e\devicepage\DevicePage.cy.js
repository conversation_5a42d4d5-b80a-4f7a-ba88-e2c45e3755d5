/// <reference types="cypress" />

// ✅ Helper to safely extract <PERSON><PERSON><PERSON> from OpenAI response
const extractJSON = (text) => {
  const match = text.match(/{[\s\S]*}/);
  if (match) {
    return JSON.parse(match[0]);
  }
  throw new Error(`Could not extract <PERSON><PERSON><PERSON> from OpenAI response: ${text}`);
};

describe("Device Page", () => {
  let firstDeviceIp = ""; // dynamic placeholder
  beforeEach(() => {
    cy.visit("/login");
    cy.get('[data-testid="username"]').type("admin");
    cy.get('[data-testid="password"]').type("default");
    cy.get('[data-testid="submit"]').click();
    cy.url().should("not.include", "/login");
    cy.visit("/devices");
    cy.intercept("GET", "/api/v1/devices").as("getDevices");
    cy.wait("@getDevices"); // Wait for devices data load

     cy.get(".ant-table-row")
      .first()
      .find("td")
      .first()
      .invoke("text")
      .then((ip) => {
        firstDeviceIp = ip.trim();
        cy.log("First Device IP:", firstDeviceIp);
      });
  });

   it("should load device table with data", () => {
    cy.get(".ant-table-row").should("have.length.at.least", 1);
    cy.get(".ant-table-row").first().find("td").first().should("have.text", firstDeviceIp);
  });

  it('should open context menu on row right-click', () => {
    cy.get('.ant-table-row').first().find('td').first().rightclick();
    cy.get('body').find('*[class*=dropdown]').should('be.visible');
  });

  it('should beep a single device via context menu', () => {
    cy.intercept('POST', '/api/v1/commands').as('beepCommand');
    cy.get('.ant-table-row').first().find('td').first().rightclick();
    cy.contains('Beep').click();
    cy.get('.ant-modal:visible').should('exist').within(() => {
      cy.contains('Confirm Beep Device').should('be.visible');
      cy.contains('button', 'OK').click();
    });
    cy.wait('@beepCommand').its('request.body').should((body) => {
      expect(body[0].command).to.match(/^beep\s/);
    });
  });

  it('should reboot a single device via context menu', () => {
    cy.intercept('POST', '/api/v1/commands').as('rebootCommand');
    cy.get('.ant-table-row').first().find('td').first().rightclick();
    cy.contains('Reboot').click();
    cy.get('.ant-modal:visible').should('exist').within(() => {
      cy.contains('Confirm Reboot Device').should('be.visible');
      cy.contains('button', 'OK').click();
    });
    cy.wait('@rebootCommand').its('request.body').should((body) => {
      expect(body[0].command).to.match(/^reset\s/);
    });
  });

  it("should select multiple devices and perform mass delete", () => {
    cy.intercept("POST", "/api/v1/commands").as("deleteCommand");
    cy.get(".ant-checkbox-input").eq(1).click();
    cy.get(".ant-table-row").first().find("td").first().rightclick();
    cy.contains("Delete Device").click();
    cy.get(".ant-modal:visible").should("exist").within(() => {
      cy.contains("Delete Device").should("be.visible");
      cy.contains("button", "OK").click();
    });
    cy.wait("@deleteCommand").its("request.body").should((body) => {
      expect(body[0].command).to.match(/^device delete\s/);
    });
  });

  it('should open firmware update modal and submit form', () => {
    cy.intercept("POST", "/api/v1/commands").as("fwUpdate");
    cy.get('.ant-checkbox-input').eq(1).click();
    cy.get(".ant-table-row").first().find("td").first().rightclick();
    cy.contains('Upload Firmware').click();
    cy.get('textarea').first().type('https://www.atoponline.com/wp-content/uploads/2017/11/EHG7504_EHG7508_EHG7512_EHG7516_EHG7520_EHG9508_EHG9512_EMG8508_EMG8510_RHG7528_RHG9528-K800A800.zip');
    cy.contains('button', 'OK').click();
    cy.wait("@fwUpdate").its("request.body").should((body) => {
      expect(body[0].command).to.match(/^firmware update\s/);
    });
  });

  it('should switch to MDR inventory type', () => {
    cy.get('.ant-segmented-item-label').contains('mdr').click();
  });

  it("should scan new devices by IP range", () => {
    cy.intercept("POST", "/api/v1/commands").as("scanCommand");
    cy.get('[data-testid="scan-ip-btn"]').click();
    cy.get(".ant-modal:visible").should("exist").within(() => {
      cy.get('input[placeholder="eg. xxx.xxx.xxx.xxx/xx"]').type("************/24");
      cy.contains("button", /^ok$/i).click();
    });
    cy.wait("@scanCommand").its("request.body").should((body) => {
      expect(body[0].command).to.match(/^scan\s/);
    });
  });

  // ✅ AI-Powered: Scan with AI-generated subnet
  it("should scan with AI-generated IP range", () => {
    cy.task("callOpenAI", {
      prompt: `Give ONLY a valid private subnet range in JSON: { "range": "192.168.x.x/xx" } for scanning test devices. No extra text.`
    }).then((aiData) => {
      const { range } = extractJSON(aiData);
      cy.log("AI-generated range:", range);

      cy.intercept("POST", "/api/v1/commands").as("aiScanCommand");
      cy.get('[data-testid="scan-ip-btn"]').click();
      cy.get(".ant-modal:visible").should("exist").within(() => {
        cy.get('input[placeholder="eg. xxx.xxx.xxx.xxx/xx"]').clear().type(range);
        cy.contains("button", /^ok$/i).click();
      });
      cy.wait("@aiScanCommand").its("request.body").should((body) => {
        expect(body[0].command).to.match(/^scan\s/);
      });
    });
  });
it("should validate reboot message using AI (via modal)", () => {
  cy.intercept("POST", "/api/v1/commands").as("rebootCommand");

  cy.get(".ant-table-row").first().find("td").first().rightclick();
  cy.contains("Reboot").click();
  cy.get(".ant-modal:visible").within(() => {
    cy.contains("Confirm Reboot Device").should("exist");
    cy.contains("button", "OK").click();
  });

  cy.wait("@rebootCommand").then(({ request }) => {
    const { command } = request.body[0];

    cy.task("callOpenAI", {
      prompt: `Given this reboot command: "${command}", respond with JSON: { "confirmation": "Device reboot command sent." }`
    }).then((aiData) => {
      const { confirmation } = extractJSON(aiData);

      // Instead of checking DOM, we show log or validate API sent
      expect(command).to.match(/^reset\s/);
      cy.log("AI confirmation suggestion:", confirmation);
    });
  });
});
it("should perform AI-suggested action on first device", () => {
  cy.task("callOpenAI", {
    prompt: `Choose one action for a device: "Beep", "Reboot", or "Delete". Return JSON only: { "action": "Beep" }`,
  }).then((aiData) => {
    const { action } = extractJSON(aiData);
    cy.get(".ant-table-row").first().find("td").first().rightclick();
    cy.contains(action).click();

    cy.get(".ant-modal:visible").within(() => {
      cy.contains("button", "OK").click();
    });

    const alias =
      action === "Beep" ? "@beepCommand" : action === "Reboot" ? "@rebootCommand" : "@deleteCommand";

    cy.intercept("POST", "/api/v1/commands").as(alias.slice(1));
    cy.wait(alias).its("request.body").should("exist");
  });
});
it("should perform a fuzzy search using AI-generated keyword", () => {
  cy.task("callOpenAI", {
    prompt: `Suggest a search keyword for a device IP or name that partially matches common IoT devices. Format JSON: { "keyword": "192.168" }`,
  }).then((aiData) => {
    const { keyword } = extractJSON(aiData);
    cy.get(".ant-input-affix-wrapper input").first().clear().type(keyword);
    cy.wait(500); 
    cy.get(".ant-table-row").its("length").should("be.greaterThan", 0);
  });
});

});
