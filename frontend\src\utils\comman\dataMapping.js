import dayjs from "dayjs";
export const convertToJsonObject = (params, flags) => {
  let obj = [];
  let flagsObject = {
    client: flags?.cc,
    tag: flags?.ct,
    kind: flags?.ck,
    nooverwrite: flags?.cno,
    all: flags?.ca,
    nosyslog: flags?.cns,
  };
  if (params.indexOf("\n") < 0) {
    let trimedParam = params.trim();
    if (!trimedParam.startsWith("#", 0))
      obj = [...obj, { command: trimedParam, ...flagsObject }];
  } else if (params.indexOf("\r\n") > 0) {
    params.split("\r\n").forEach((item) => {
      if (item !== "") {
        let trimedItem = item.trim();
        if (!trimedItem.startsWith("#", 0))
          obj = [...obj, { command: trimedItem, ...flagsObject }];
      }
    });
  } else {
    params.split("\n").forEach((item) => {
      if (item !== "") {
        let trimedItem = item.trim();
        if (!trimedItem.startsWith("#", 0))
          obj = [...obj, { command: trimedItem, ...flagsObject }];
      }
    });
  }
  let json = JSON.stringify(obj);

  return json;
};

export const getTopologyClient = (data) => {
  return Object.keys(data);
};
export const getAllTopologyData = (data) => {
  let nodesa = [];
  let linksa = [];
  let linkData = [];
  let edgeunique = [];
  const keys = Object.keys(data);
  keys.forEach((key) => {
    let { nodes, links } = getTopologyDataByClient(data, key);
    nodesa = [...nodesa, ...nodes];
    linksa = [...linksa, ...links];
  });
  linksa.forEach((item) => {
    if (item.blockedPort === "true" && !edgeunique.includes(item.edge)) {
      edgeunique = [...edgeunique, item.edge];
      linkData = [...linkData, item];
    }
  });
  linksa.forEach((item) => {
    if (!edgeunique.includes(item.edge) && item.targetPort !== "") {
      edgeunique = [...edgeunique, item.edge];
      linkData = [...linkData, item];
    }
  });
  return { nodes: nodesa, links: linkData };
};

export const getTopologyDataByClient = (data, client) => {
  let nodeData = [];
  let linkData = [];
  let tempLinkData = [];
  let edgeunique = [];
  data[client].forEach((item) => {
    nodeData = [
      ...nodeData,
      {
        id: item.id,
        ipAddress: item.ipAddress,
        modelname: item.modelname,
        macAddress: item.macAddress,
      },
    ];
    if (item?.linkData) {
      tempLinkData = [...tempLinkData, ...item.linkData];
    }
  });

  tempLinkData.forEach((item) => {
    if (item.blockedPort === "true" && !edgeunique.includes(item.edge)) {
      edgeunique = [...edgeunique, item.edge];
      linkData = [...linkData, item];
    }
  });
  tempLinkData.forEach((item) => {
    if (!edgeunique.includes(item.edge) && item.targetPort !== "") {
      edgeunique = [...edgeunique, item.edge];
      linkData = [...linkData, item];
    }
  });
  console.log("link data", linkData);
  //return { nodeData, tempLinkData, linkData };
  return { nodes: nodeData, links: linkData };
};

export const checkTimestampDiff = (currentData = []) => {
  const currentDt = dayjs().unix();
  const newData = currentData
    .map((item) => {
      let timeDiff = currentDt - Number(item.timestamp);
      return { ...item, timeDiff };
    })
    .filter((item) => item.mac !== "11-22-33-44-55-66");
  return newData;
};

export const mapDeviceDataForExportOnOff = (deviceData = []) => {
  return deviceData.map((data) => {
    const onoroff = data.timeDiff > 90 ? "offline" : "online";
    return { ...data, timeDiff: onoroff };
  });
};

export const ParseJsonToTopoData = (topodata) => {
  const oData = Object.values(topodata);
  const groupByNetworkServices = oData.reduce((group, topo) => {
    const { services } = topo;
    group[services] = group[services] ?? [];
    group[services].push(topo);
    return group;
  }, {});
  return groupByNetworkServices;
};

export const createManualTpologyData = (values) => {
  const id = values.mac.trim();
  const linkData = values.linkData.map((item) => ({
    source: id,
    target: item.target.trim(),
    sourcePort: item.sourcePort.trim(),
    targetPort: item.targetPort.trim(),
    blockedPort: "false",
    linkType: "manual",
    edge: createEdge(id, item.target.trim()),
  }));
  return { id, topoType: "manual", linkData };
};

const createEdge = (source, target) => {
  let edgeData;
  if (source < target) {
    edgeData = source + "_" + target;
  } else {
    edgeData = target + "_" + source;
  }
  return edgeData;
};

export function getServiceNameByKind(data, targetKind) {
  if (!data) return [];

  const result = Object.values(data)
    .filter((item) => item.kind === targetKind ?? item.name)
    .map((item) => ({ value: item.name, label: item.name }));
  return result;
}

/**
 * Inserts a newline character ('\n') after every N characters in a very long string
 * using a regular expression.
 *
 * @param {string} longText The input string to wrap.
 * @param {number} [charLimit=20] The number of characters after which to insert a newline. Defaults to 20.
 * @returns {string} The wrapped text with newlines inserted.
 */
export function insertNewlinesRegex(longText, charLimit = 20) {
  if (typeof longText !== "string" || longText === null) {
    return ""; // Handle non-string or null input gracefully
  }

  // The regex `.{1,${charLimit}}` matches any character (.),
  // from 1 up to `charLimit` times. The 'g' flag ensures all matches are found.
  const regex = new RegExp(`.{1,${charLimit}}`, "g");

  // .match() returns an array of matches, or null if no matches.
  // Use || [] to ensure it's always an array.
  const chunks = longText.match(regex) || [];

  let wrappedText = chunks.join("\n");

  // Same logic as before: if the original text length is a perfect multiple of charLimit
  // and not empty, add a trailing newline.
  if (longText.length > 0 && longText.length % charLimit === 0) {
    wrappedText += "\n";
  }

  return wrappedText;
}
