import { But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lex, <PERSON><PERSON>, Segmented, Typography } from "antd";
import { useThemeStore } from "./useStore";
import ColorSwatch from "./ColorSwatch";
import { ThemeIcon } from "../../components/comman/CustomsIcons";

const options = [
  { label: "Auto", value: "auto" },
  { label: "Light", value: "light" },
  { label: "Dark", value: "dark" },
];

export default () => {
  const { changeMode, changePrimaryColor, mode } = useThemeStore();
  const content = (
    <Flex vertical align="center" gap={4}>
      <Typography.Text>Change Color Mode</Typography.Text>
      <Segmented
        value={mode}
        onChange={(v) => changeMode(v)}
        options={options}
      />
      <Divider style={{ margin: "8px 0" }} />
      <Typography.Text>Change Primary Color</Typography.Text>
      <ColorSwatch />
    </Flex>
  );
  return (
    <Popover
      placement="bottom"
      title="NIMBL Settings"
      content={content}
      trigger="click"
      showArrow={false}
      style={{ width: "300px" }}
    >
      <Button type="default" aria-label="theme setting" icon={<ThemeIcon />} />
    </Popover>
  );
};
