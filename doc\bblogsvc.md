# bblogsvc

Log Service

The `bblogsvc` is a distributed log service designed to handle high-volume logs from various nimbl services. Its primary function is to store these logs efficiently, while also providing access to a range of logs as needed.

### Log Flow

While the log flow can vary based on specific requirements, a typical configuration might include the following paths:

#### Normal Case: Nmssvc to rootsvc

In most scenarios, logs are sent directly from the `nmssvc` to the `rootsvc`. This is the standard path for log data in our system.

#### High Volume Logs: Direct to a Separate Syslog Service

In cases where there is a high volume of log traffic, logs can be sent directly to a separate syslog service. This approach helps prevent the `rootsvc` from becoming overloaded with log data.

#### Optional: rootsvc to Another Syslog Service

Optionally, logs can be forwarded from the `rootsvc` to another syslog service. This configuration can be useful for offloading log analysis tasks to a separate service, thereby preventing potential overloads on the `rootsvc`.

## Quick Start

Step 1. Run root service

```shell
$ bbrootsvc -n root
```

Step 2. Run log service

```shell
$ bblogsvc -n logsvc -r {root_service_url}
```

### Example 1

Here we provide an example of running idp svc and send logs to log svc

```shell
$ bbrootsvc -n root
$ bblogsvc -n logsvc -r http://localhost:27182
$ bbidpsvc -n idpsvc -r http://localhost:27182 -rs localhost:5524
```

The port `5524` stands for bblogsvc default syslog server port.
The `-rs` flag can specify where logs forward to. In this case we send idpsvc logs to logsvc because idp service may produce lots of logs. So it will be like backup high volume logs only.

### Example 2

Here is another example of running anomaly svc and log svc in order to analyze logs.

```shell
$ bbrootsvc -n root
$ bblogsvc -n logsvc -r http://localhost:27182 -rs localhost:5514
$ bbnmssvc -n nmssvc -r http://localhost:27182 -rs localhost:5514
$ bbanomaly -n anomaly -r http://localhost:27182 -rs localhost:5524
```

This example shows that nmssvc first send logs to logsvc then logsvc forwards logs to root.
By defauly logsvc will keep logs no matter forwarded not.

Then issue command

```shell
$ bbctl -cc logsvc syslog list
```

This command will list every log file in result with json format. Let's say the result is `syslog_mnms.log`
Once we have this file name, we can issue command to get an URL of requested spec

```shell
$ bbctl -cc logsvc syslog export syslog_mnms.log 2024/03/06 00:00:00 2024/03/07 12:00:00 1000
```

This means give me `1000` logs from `2024/03/06 00:00:00` to `2024/03/07 12:00:00`.
Or you can issue with all.

Later on, the command result will be an URL, probably like
`http://localhost:27195/api/v1/files/syslogs/syslog_1709638445.log`, then we can issue anomaly svc to analyze

```shell
$ bbctl -cc anomaly detect http://localhost:27195/api/v1/files/syslogs/syslog_1709638445.log
```

Therefore, an anomaly report will be generated according to this file.

## Command Line Flags

## bblogsvc Command Line Flags

You can use the following command line flags with the `bblogsvc` executable:

- `-O string`: Set the debug log output (default is "stderr").
- `-P string`: Set the debug log pattern string.
- `-daemon string`: Control the daemon process. Options include "run", "start", "stop", "restart", "install", "uninstall", and "status".
- `-debuglog`: Enable the debug log. This will override `-P` to `.*`.
- `-ds`: Dump stack trace when exiting with a non-zero code.
- `-ic int`: Set the command processing interval (default is 5).
- `-ir int`: Set the service registration interval (default is 60).
- `-n string`: Set the name.
- `-nohttp`: Disable the HTTP service.
- `-ns string`: Specify the NIMBL service URL (default is "http://localhost:27195").
- `-p int`: Set the port (default is 27195).
- `-r string`: Set the root URL.
- `-rs string`: Set the remote syslog server address.
- `-sbk`: Enable backup of syslog after forwarding (default is true).
- `-sc`: Enable compression of backup syslog files.
- `-sf uint`: Set the file size (in megabytes) of syslog (default is 100).
- `-so string`: Set the local path of syslog (default is "syslog_mnms.log").
- `-ss string`: Set the syslog server address (default is ":5524").
- `-version`: Print the version.

To display this help information, use the `-h` flag:

```shell
./bblogsvc/bblogsvc.exe -h

## Command APIs

`bblogsvc` can issue command to work or change settings

### Set syslog service settings

#### syslog path
Usage : syslog config path [path]

    [path]        : syslog file path

Example :

    syslog config path /tmp/syslog.log

#### syslog file maximum size
Usage : syslog config maxsize [maxsize]

    [maxsize]     : syslog file maxsize size in MB

Example :

    syslog config maxsize 100

#### syslog backup file compressed
Usage : syslog config compress [compress]

    [compress]    : syslog file compress

Example :

    syslog config compress true

#### syslog remote server address
Usage : syslog config remote [server address]

    [server address] : remote syslog server address

Example :

    syslog config remote :5514

#### syslog backup after forwarding
Usage : syslog config backup-after-forward [enable]

    [enable]      : syslog file bak after fwd, true/false

Example :

    syslog config backup-after-forward true

### Syslog service features

#### List log files in syslog path
Usage : syslog list

#### Export syslog file
Export syslog file to url. Max size is 10MB.

Usage : syslog export [source filename] [spec]

    [source filename]     : Log file name in the syslog path, can be listed by syslog list, or use all to query all log files

    [spec]                : Spec of log file, can be start date, start time, end date, end time, max line, if without max line, that mean read all of lines.

Example :

    syslog export syslog_mnms.log

    syslog export syslog_mnms.log 5

    syslog export syslog_mnms.log 2023/02/21 22:06:00 2023/02/22 22:08:00

    syslog export syslog_mnms.log 2023/02/21 22:06:00 2023/02/22 22:08:00 5

    syslog export all 2023/02/21 22:06:00 2023/02/22 22:08:00

    syslog export all 2023/02/21 22:06:00 2023/02/22 22:08:00 100
```
