<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .mermaid-container {
            padding: 1em;
            background-color: #f1f3f4;
            border-radius: 8px;
            margin: 1em 0;
        }
        .mermaid-container svg {
            display: block;
            margin: auto;
            max-width: 100%;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Mermaid Graph Rendering Test</h1>
    
    <div class="test-container">
        <h2>Test 1: Your Network Graph</h2>
        <p>This should render as a visual graph, not as text:</p>
        <pre><code>graph LR;
N_00_60_E9_1A_3C_3E["'EHG7508-4SFP'<br/>IP: *********"] -->|"Port5 to Port 7"| N_00_60_E9_20_C3_03["'EHG7512-8PoE-410GSFP'<br/>IP: *********"];
N_00_60_E9_20_C3_03 -->|"Port7 to Port 5"| N_00_60_E9_1A_3C_3E;
N_00_60_E9_20_C3_03 -->|"Port6 to Port 1"| N_00_60_E9_26_2D_C6["'EHG2408'<br/>IP: **********"];
N_00_60_E9_26_2D_C6 -->|"Port1 to Port 6"| N_00_60_E9_20_C3_03;</code></pre>
    </div>

    <div class="test-container">
        <h2>Test 2: Simple Graph</h2>
        <p>A simple test graph:</p>
        <pre><code>graph TD;
A[Start] --> B{Decision};
B -->|Yes| C[Action 1];
B -->|No| D[Action 2];
C --> E[End];
D --> E;</code></pre>
    </div>

    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs';
        
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
        });

        // Function to render mermaid diagrams
        async function renderMermaidDiagrams() {
            const codeElements = document.querySelectorAll('pre code');
            
            for (let i = 0; i < codeElements.length; i++) {
                const el = codeElements[i];
                const codeText = el.textContent.trim();
                
                // Check if it's a mermaid diagram
                const mermaidKeywords = ['graph', 'flowchart', 'sequenceDiagram', 'classDiagram'];
                const isMermaid = mermaidKeywords.some(keyword => codeText.startsWith(keyword));
                
                if (isMermaid) {
                    try {
                        const graphId = `mermaid-test-${i}`;
                        const { svg } = await mermaid.render(graphId, codeText);
                        
                        const graphContainer = document.createElement('div');
                        graphContainer.classList.add('mermaid-container');
                        graphContainer.innerHTML = svg;
                        
                        el.parentElement.replaceWith(graphContainer);
                    } catch (error) {
                        console.error('Mermaid rendering error:', error);
                        el.parentElement.innerHTML = `<div style="color: red; background: #ffebee; padding: 10px; border-radius: 4px;">
                            Mermaid Error: ${error.message}
                        </div>`;
                    }
                }
            }
        }

        // Render diagrams when page loads
        document.addEventListener('DOMContentLoaded', renderMermaidDiagrams);
    </script>
</body>
</html>
