package main

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"mnms"
	"os"
	"regexp"
	"runtime/debug"
	"sort"
	"strings"
	"time"

	"github.com/qeof/q"
)

var Version string

func printVersion() {
	if Version == "" {
		Version = "Development version"
	}
	// print out version
	fmt.Fprintf(os.<PERSON>derr, "BlackBear NIMBL Version: %s\n", Version)
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintln(os.Stderr, "Build information:")
	fmt.Fprintln(os.Stderr, info.GoVersion, info.Main, info.Settings)
}

// Error handling utilities
func exitWithError(msg string, args ...interface{}) {
	fmt.Fprintf(os.Stderr, "error: "+msg+"\n", args...)
	mnms.DoExit(1)
}

func exitWithMessage(msg string, args ...interface{}) {
	fmt.Fprintf(os.<PERSON>, msg+"\n", args...)
	mnms.DoExit(0)
}

func validateRequiredArgs(args []string, minLen int, helpFunc func()) {
	if len(args) < minLen {
		helpFunc()
	}
}

// Command handlers
func handleCmdList(args []string, localRootURL *string) {
	validateRequiredArgs(args, 3, func() {
		exitWithError("insufficient args for cmd list\nUsage: bbctl cmd list <command>\nExample: bbctl cmd list <command> or bbctl cmd list all")
	})

	url := buildCommandListURL(args, localRootURL)
	cmds := fetchCommands(url)
	displayCommands(cmds)
}

func handleCmdClear(args []string, localRootURL *string) {
	validateRequiredArgs(args, 3, func() {
		exitWithError("insufficient args for cmd clear\nUsage: bbctl cmd clear <command>\nExample: bbctl cmd clear <command> or bbctl cmd clear all")
	})

	cmds := prepareClearCommands(args, localRootURL)
	clearCommands(cmds, localRootURL)
	exitWithMessage("Commands cleared successfully.")
}

func buildCommandListURL(args []string, localRootURL *string) string {
	if args[2] == "all" {
		return *localRootURL + "/api/v1/commands?cmd=all"
	}
	return *localRootURL + "/api/v1/commands?cmd=" + strings.Join(args[2:], "%20")
}

func fetchCommands(url string) map[string]mnms.CmdInfo {
	resp, err := mnms.GetWithToken(url, mnms.QC.AdminToken)
	if err != nil {
		exitWithError("cannot connect to root server at %v", url)
	}
	if resp == nil {
		exitWithError("no response from root server")
	}
	defer resp.Body.Close()

	cmds := make(map[string]mnms.CmdInfo)
	// if err := json.NewDecoder(resp.Body).Decode(&cmds); err != nil {
	// 	q.Q(err.Error())
	// 	exitWithError("cannot parse response from root server")
	// }
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		exitWithError("cannot read response body from root server: %v", err)
	}
	q.Q("response body from root server", string(body))
	if err := json.Unmarshal(body, &cmds); err != nil {
		exitWithError("cannot parse response from root server: %v", err)
	}

	// no commands found while list is not an error
	if len(cmds) == 0 {
		exitWithMessage("no command found from root server")
	}
	return cmds
}

func displayCommands(cmds map[string]mnms.CmdInfo) {
	// Convert map to slice for consistent ordering
	var cmdList []mnms.CmdInfo
	for _, cmdInfo := range cmds {
		cmdList = append(cmdList, cmdInfo)
	}

	// Sort by timestamp (newest first) if available, otherwise by command
	sort.Slice(cmdList, func(i, j int) bool {
		if cmdList[i].Timestamp != "" && cmdList[j].Timestamp != "" {
			return cmdList[i].Timestamp > cmdList[j].Timestamp
		}
		return cmdList[i].Command < cmdList[j].Command
	})

	const pageSize = 10
	totalCommands := len(cmdList)

	// If there are few commands, display all at once
	if totalCommands <= pageSize {
		for _, cmdInfo := range cmdList {
			displaySingleCommand(cmdInfo)
		}
		mnms.DoExit(0)
		return
	}

	// Simple pagination
	currentPage := 0
	totalPages := (totalCommands + pageSize - 1) / pageSize
	reader := bufio.NewReader(os.Stdin)

	for {
		// Clear screen
		fmt.Print("\033[2J\033[H")

		// Display commands for current page
		start := currentPage * pageSize
		end := start + pageSize
		if end > totalCommands {
			end = totalCommands
		}

		for i := start; i < end; i++ {
			displaySingleCommand(cmdList[i])
		}

		// Display page info and navigation at bottom
		fmt.Fprintf(os.Stderr, "\n=== Page %d/%d, Total: %d commands ===\n",
			currentPage+1, totalPages, totalCommands)
		fmt.Fprintf(os.Stderr, "[Enter] Next page")
		if currentPage > 0 {
			fmt.Fprintf(os.Stderr, " | [p] Previous page")
		}
		fmt.Fprintf(os.Stderr, " | [q] Quit: ")

		// Read user input
		input, err := reader.ReadString('\n')
		if err != nil {
			mnms.DoExit(1)
		}
		input = strings.TrimSpace(strings.ToLower(input))

		switch input {
		case "", "n": // Enter or 'n' for next
			if currentPage < totalPages-1 {
				currentPage++
			} else {
				fmt.Fprintf(os.Stderr, "Reached last page. Press Enter to continue or 'q' to quit: ")
				reader.ReadString('\n')
			}
		case "p": // Previous page
			if currentPage > 0 {
				currentPage--
			}
		case "q": // Quit
			mnms.DoExit(0)
		default:
			// Treat any other input as next page
			if currentPage < totalPages-1 {
				currentPage++
			}
		}
	}
}

func displaySingleCommand(cmdInfo mnms.CmdInfo) {
	fmt.Fprintf(os.Stderr, "Command: %s\n", cmdInfo.Command)
	fmt.Fprintf(os.Stderr, "Status: %s\n", cmdInfo.Status)
	if cmdInfo.Timestamp != "" {
		fmt.Fprintf(os.Stderr, "Timestamp: %s\n", cmdInfo.Timestamp)
	}
	if len(cmdInfo.Result) > 0 {
		fmt.Fprintf(os.Stderr, "Result: %s\n", PrettyPrint(cmdInfo.Result))
	}
	fmt.Fprintln(os.Stderr, "-----------------------------")
}

func prepareClearCommands(args []string, localRootURL *string) []mnms.CmdInfo {
	cmds := []mnms.CmdInfo{}
	url := *localRootURL + "/api/v1/commands"

	if args[2] == "all" {
		url += "?cmd=all"
		retCmds := fetchCommands(url)
		for _, cmdInfo := range retCmds {
			if cmdInfo.Command != "" {
				cmd := mnms.CmdInfo{
					Command: cmdInfo.Command,
					Edit:    "delete",
				}
				cmds = append(cmds, cmd)
			}
		}
	} else {
		cmd := strings.Join(args[2:], " ")
		cmdInfo := mnms.CmdInfo{
			Command: cmd,
			Edit:    "delete",
		}
		cmds = append(cmds, cmdInfo)
	}
	return cmds
}

func clearCommands(cmds []mnms.CmdInfo, localRootURL *string) {
	jsonBytes, err := json.Marshal(cmds)
	if err != nil {
		exitWithError("cannot marshal commands to json: %v", err)
	}

	url := *localRootURL + "/api/v1/commands"
	resp, err := mnms.PostWithToken(url, mnms.QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		exitWithError("cannot connect to root server at %v", *localRootURL)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		exitWithError("cannot clear commands, status code %d\nResponse: %s", resp.StatusCode, body)
	}
}

func main() {
	flagversion := flag.Bool("version", false, "print version")
	cmdflagnoow := flag.Bool("cno", false, "command overwrite flag")
	cmdflagall := flag.Bool("ca", false, "command all flag")
	cmdflagnosys := flag.Bool("cns", false, "command no syslog flag")
	cmdClient := flag.String("cc", "", "command network service name specification")
	cmdTag := flag.String("ct", "", "command tag")
	localRootURL := flag.String("r", fmt.Sprintf("http://localhost:%d", mnms.QC.Port), "address of the root service")
	ck := flag.String("ck", "usercommand", "kinds of command")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	dp := flag.String("P", "", "debug log pattern string")
	flag.StringVar(&q.O, "O", "stderr", "debug log output")
	waitSec := flag.Int("w", 10, "wait seconds for command result, 0 means no wait")

	flag.Parse()
	if *flagversion {
		printVersion()
		mnms.DoExit(0)
	}

	// version
	if Version != "" {
		mnms.QC.Version = Version
	}

	if *debuglog {
		*dp = ".*"
	}

	if *dp == "." {
		fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
		mnms.DoExit(1)
	}
	_, err := regexp.Compile(*dp)
	if err != nil {
		fmt.Fprintf(os.Stderr, "error: invalid regular expression, %v\n", err)
		mnms.DoExit(1)
	}
	q.P = *dp
	q.Q(q.O, q.P)

	args := flag.Args()
	CheckArgs(args)
	// implement cli by posting commands via http api
	acmd := args[0]
	found := false
	for _, c := range mnms.ValidCommands {
		if c == acmd {
			found = true
		}
	}
	if acmd == "cmd" {
		found = true // 'cmd' is a bbctl command
	}

	if !found {
		fmt.Fprintf(os.Stderr, "error: invalid cmd %s\n\n", acmd)
		helpMsg := mnms.HelpCmd("help")
		fmt.Fprintf(os.Stderr, "%s\n", helpMsg)
		mnms.DoExit(1)
	}
	if len(args) < 2 {
		fmt.Fprintf(os.Stderr, "error: insufficient args\n")
		helpMsg := mnms.HelpCmd("help " + acmd)
		fmt.Fprintf(os.Stderr, "%s\n", helpMsg)
		mnms.DoExit(1)
	}
	if args[1] == "help" {
		for _, c := range mnms.ValidCommands {
			if c == acmd {
				fmt.Println(acmd)
				helpMsg := mnms.HelpCmd("help " + acmd)
				fmt.Fprintf(os.Stderr, "%s\n", helpMsg)
				mnms.DoExit(0)
			}
		}
	}

	// check args length > 1 and args[1] is a 'util'
	if len(os.Args) > 2 && os.Args[1] == "util" && os.Args[2] != "help" {
		err := mnms.ProcessDirectCommands()
		if err != nil {
			fmt.Fprintln(os.Stderr, err)
		}
		mnms.DoExit(0)
		return
	}

	mnms.QC.AdminToken, err = mnms.GetToken("admin")
	if err != nil {
		q.Q(err)
		fmt.Fprintln(os.Stderr, "error: can't get admin token")
		mnms.DoExit(1)
	}

	// exceptional handling for 'cmd' command
	if len(args) > 0 && args[0] == "cmd" {
		if len(args) > 1 {
			switch args[1] {
			case "list":
				handleCmdList(args, localRootURL)
			case "clear":
				handleCmdClear(args, localRootURL)
			default:
				fmt.Fprintf(os.Stderr, "error: invalid command %s\n", args[1])
				helpMsg := mnms.HelpCmd("help cmd")
				fmt.Fprintf(os.Stderr, "%s\n", helpMsg)
				mnms.DoExit(1)
			}
		}
	}

	cmd := strings.Join(args, " ")
	// if *cmdClient != "" {
	// 	// For -cc option, the command is sent to the specified service
	// 	// command map's key is not just command string but also include client name "@ client-name command"
	// 	kcmd = "@" + *cmdClient + " " + cmd
	// 	// kcmd = fmt.Sprintf("%v -cc %v", kcmd, *cmdClient)
	// }
	ci := mnms.CmdInfo{
		Timestamp:   time.Now().Format(time.RFC3339),
		Command:     cmd,
		NoOverwrite: *cmdflagnoow,
		All:         *cmdflagall,
		NoSyslog:    *cmdflagnosys,
		Kind:        *ck,
		Client:      *cmdClient,
		Tag:         *cmdTag,
	}
	cmdList := []mnms.CmdInfo{ci}
	jsonBytes, err := json.Marshal(cmdList)
	if err != nil {
		q.Q(err)
		mnms.DoExit(1)
	}
	q.Q("posting", cmdList)
	url := *localRootURL + "/api/v1/commands"
	resp, err := mnms.PostWithToken(url, mnms.QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q(err.Error())
		fmt.Fprintf(os.Stderr, "error: cannot connect to root server at %v\n", *localRootURL)
		mnms.DoExit(1)
	}
	if resp == nil {
		fmt.Fprintf(os.Stderr, "error: no response from root server at %v\n", localRootURL)
		mnms.DoExit(1)
	}

	// save close, resp should be nil here
	defer resp.Body.Close()
	result, err := io.ReadAll(resp.Body)
	if err != nil {
		q.Q(err.Error())
		fmt.Fprintf(os.Stderr, "error: reading response from root server at %v\n", localRootURL)
		mnms.DoExit(1)
	}
	q.Q(string(result))
	// result is an array of CmdInfo
	var retCmdInfos []mnms.CmdInfo
	err = json.Unmarshal(result, &retCmdInfos)
	if err != nil {
		q.Q(err.Error())
		fmt.Fprintf(os.Stderr, "error: cannot parse response from root server at %v\n", localRootURL)
		mnms.DoExit(1)
	}
	if len(retCmdInfos) == 0 {
		fmt.Fprintf(os.Stderr, "error: no command result from root server at %v\n", localRootURL)
		mnms.DoExit(1)
	}
	for _, retCmdInfo := range retCmdInfos {
		fmt.Fprintf(os.Stderr, "command [%s] successfully created\n", retCmdInfo.Command)
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(*waitSec)*time.Second)
	if *waitSec == 0 {
		// if waitSec is 0, we don't wait for the command result
		cancel() // Cancel immediately
		mnms.DoExit(0)
		return
	}
	if *waitSec < 0 {
		// if waitSec is negative, wait indefinitely
		ctx = context.Background()
		cancel = func() {}
	}
	q.Q("context created with timeout", *waitSec, "seconds")
	defer cancel()

	q.Q("waiting for command result ", retCmdInfos)
	for _, retCmdInfo := range retCmdInfos {
		// Start spinner for this command
		spinnerDone := make(chan struct{})
		go func() {
			frames := []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"}
			ticker := time.NewTicker(80 * time.Millisecond)
			defer ticker.Stop()

			idx := 0
			for {
				select {
				case <-ticker.C:
					fmt.Fprintf(os.Stderr, "\r%s Waiting for command...", frames[idx])
					idx = (idx + 1) % len(frames)
				case <-spinnerDone:
					fmt.Fprintf(os.Stderr, "\r")
					return
				case <-ctx.Done():
					fmt.Fprintf(os.Stderr, "\r")
					return
				}
			}
		}()
		// Query command until finished
		cmdResult, err := mnms.QueryCmdTilFinished(ctx, *localRootURL, time.Second, retCmdInfo.Command)

		// Stop spinner
		close(spinnerDone)

		if err != nil {
			q.Q(err.Error())
			fmt.Fprintf(os.Stderr, "error: %s\n", err.Error())
			continue
		}

		if cmdResult == nil {
			fmt.Fprintf(os.Stderr, "error: command %s not found or not finished\n", retCmdInfo.Command)
			continue
		}

		fmt.Fprintf(os.Stderr, "\nStatus: %s\n", cmdResult.Status)
		if len(cmdResult.Result) > 0 {
			fmt.Fprintf(os.Stderr, "\nResult: %s\n", PrettyPrint(cmdResult.Result))
		}

	}

	// automatically update service
	mnms.QC.RootURL = *localRootURL
	rootVersionFwFileName, err := mnms.CheckRootSvcVersion()
	if err != nil {
		q.Q(err)
	} else {
		rootVersion, err := mnms.FindVersion(rootVersionFwFileName)
		if err != nil {
			q.Q(err)
			rootVersion = rootVersionFwFileName
		}
		fmt.Fprintf(os.Stderr, "The bbctl %s has the latest version %s, while the current version is %s. If you wish to update to %s, please refer to the user manual, Chapter 6.20.1.",
			mnms.QC.Name, rootVersion, mnms.QC.Version, rootVersion)
	}

	mnms.DoExit(0)

}

// PrettyPrint
func PrettyPrint(i interface{}) string {
	switch i.(type) {
	case string:
		if strings.HasPrefix(i.(string), "{") || strings.HasPrefix(i.(string), "[") {
			// maybe json
			var out bytes.Buffer
			err := json.Indent(&out, []byte(i.(string)), "", "  ")
			if err != nil {
				return i.(string)
			}
			return out.String()
		}
		return i.(string)
	default:
		s, _ := json.MarshalIndent(i, "", "\t")
		return string(s)
	}
}
