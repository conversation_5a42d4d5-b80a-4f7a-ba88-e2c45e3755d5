import { api } from "./api";

export const commandApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getAllCommands: builder.query({
      query: () => "api/v1/commands?cmd=all",
      providesTags: ["commands"],
      transformResponse: (resp) => {
        let modifiedData = [];
        Object.entries(resp).forEach(([key, value]) => {
          modifiedData = [...modifiedData, { cmd_key: key, ...value }];
        });
        return modifiedData;
      },
    }),
    sendCommand: builder.mutation({
      query: (data) => ({
        url: `api/v1/commands`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["commands"],
    }),
    getCommandResult: builder.query({
      query: ({ cValue, pageSize = 10, pageNum = 1, totalPage = 5 }) =>
        `api/v1/commands?cmd=${encodeURIComponent(
          cValue
        )}&pageSize=${pageSize}&pageNum=${pageNum}&totalPage=${totalPage}`,
      providesTags: ["commands"],
    }),
    getPortInfo: builder.query({
      query: () => "api/v1/agent/ports",
      providesTags: ["ports"],
    }),
    getTunnelInfo: builder.query({
      query: () => "api/v1/ssh/tunnels",
      providesTags: ["commands"],
      transformResponse: (resp) => Object.values(resp),
    }),
    getAllKeyStore: builder.query({
      query: () => "api/v1/kvstore/export",
      providesTags: ["keystore"],
    }),
    importKeyStore: builder.mutation({
      query: (data) => ({
        url: `api/v1/kvstore/import`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["keystore"],
    }),
    getInventries: builder.query({
      query: (params) => {
        // Handle both old format (string) and new format (object)
        if (typeof params === "string") {
          return "api/v1/devices";
        }

        const { inventoryType, groupid } = params;
        let url = "api/v1/devices";

        if (groupid && groupid !== "all") {
          url += `?groupid=${encodeURIComponent(groupid)}`;
        }

        return url;
      },
      providesTags: (result, error, params) => {
        const invType =
          typeof params === "string" ? params : params.inventoryType;
        const groupid =
          typeof params === "string" ? "all" : params.groupid || "all";
        return ["devices", invType, { type: "devices", id: groupid }];
      },
      transformResponse: (resp, meta, params) => {
        const invType =
          typeof params === "string" ? params : params.inventoryType;
        return Object.values(resp).filter((item) =>
          invType === "mdr"
            ? item.type === "motor-ctrl-card"
            : item.type !== "motor-ctrl-card" &&
              item.mac !== "11-22-33-44-55-66"
        );
      },
    }),
    addTopology: builder.mutation({
      query: (data) => ({
        url: `api/v1/topology`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["topology"],
    }),
    saveRestoreTopology: builder.mutation({
      query: (data) => ({
        url: `api/v1/topology/action`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["topology"],
    }),
  }),
});

export const {
  useGetAllCommandsQuery,
  useSendCommandMutation,
  useGetCommandResultQuery,
  useGetPortInfoQuery,
  useGetTunnelInfoQuery,
  useGetAllKeyStoreQuery,
  useImportKeyStoreMutation,
  useGetInventriesQuery,
  useAddTopologyMutation,
  useSaveRestoreTopologyMutation,
} = commandApi;
