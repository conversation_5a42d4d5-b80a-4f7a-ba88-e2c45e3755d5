//go:build !ci
// +build !ci

package mnms

import (
	"os"
	"testing"
	"time"

	"github.com/qeof/q"
)

// Note that Wireguard needs to be installed on the system for this test to work
// also run as root or sudo

func TestWg(t *testing.T) {
	QC.Name = "testroot"
	WgInit("test-wg0", true)

	// wg config
	ci := CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   "wg config interface set 10.253.0.1/24 55820",
		Kind:      "root",
	}
	RunCmd(&ci)
	ci = CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   "wg config peer add WwmOjlaOCDrU9cQmyH9Xv3GIe8DQ8T2IwWgUWrEdYwM= 10.253.0.9/32",
		Kind:      "root",
	}
	RunCmd(&ci)
	// get config
	q.Q(QC.WgData)

	// generate file
	ci = CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   "wg config generate",
		Kind:      "root",
	}
	RunCmd(&ci)
	time.Sleep(1 * time.Second)

	//
	if _, err := os.Stat("test-wg0.conf"); os.IsNotExist(err) {
		t.Fatalf("file not generated")
	}
	// wireguard destroy
	defer func() {
		os.Remove("test-wg0.conf")
	}()
	time.Sleep(1 * time.Second)

	// wireguard start
	ci = CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   "wg start",
		Kind:      "root",
	}
	RunCmd(&ci)
	time.Sleep(1 * time.Second)

	// check status
	status, err := WgStatus()
	if err != nil {
		q.Q(err)
		t.Fatal(err)
	}
	if status.Interface != "test-wg0" {
		t.Fatalf("interface not set")
	}
	time.Sleep(1 * time.Second)

	// wireguard stop
	ci = CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   "wg stop",
		Kind:      "root",
	}
	RunCmd(&ci)
	time.Sleep(1 * time.Second)

	// check status
	status, err = WgStatus()
	if err != nil {
		q.Q(err)
	}
	if status.Interface != "" {
		t.Fatalf("wg stopped")
	}
}
