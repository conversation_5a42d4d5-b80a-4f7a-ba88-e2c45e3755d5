package mnms

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"slices"
	"strings"
	"sync"

	"github.com/go-chi/chi/v5"
	"github.com/qeof/q"
)

var KVStore = make(map[string]string)

// mutex for KVStore
var KVStoreMutex = sync.Mutex{}

// GetKVValues get values from kvstore
func GetKVValues(keys []string) (map[string]string, error) {
	values := make(map[string]string)
	keyNotFound := make([]string, 0)
	for _, key := range keys {
		// check if key exists in KVStore
		if value, ok := KVStore[key]; ok {
			values[key] = value
		} else {
			// if key not exists, return error
			keyNotFound = append(keyNotFound, key)
		}
	}
	if len(keyNotFound) > 0 {
		return nil, fmt.Errorf("key not found: %v", keyNotFound)
	}
	return values, nil
}

// SetKVValues set values to kvstore
func SetKVValues(values map[string]string) {
	KVStoreMutex.Lock()
	defer KVStoreMutex.Unlock()
	for key, value := range values {
		KVStore[key] = value
	}
}

var KVStoreKeyPattern = regexp.MustCompile(`(?:^|\s):([A-Za-z][A-Za-z0-9\-_]*)`)

// ExpandCommandKVValue replace :key placehokders whith values from the KVstore API.
// Keys can only contain English letters, numbers, hyphens (-), and underscores (_).
// Returns the replaced command
func ExpandCommandKVValue(command string) (string, error) {
	// Find all matches in the command string
	matches := KVStoreKeyPattern.FindAllStringSubmatch(command, -1)
	q.Q("ReplaceKeysInCommand matches: %v", matches)
	if len(matches) == 0 {

		return command, nil // No placeholders found; return the original command
	}

	keys := make([]string, 0)
	// Process each match
	for _, match := range matches {
		key := match[1] // The key inside the placeholder, e.g., dev1-mac

		// if key exists in keys continue
		if slices.Contains(keys, key) {
			continue
		}
		keys = append(keys, key)
	}
	q.Q("keys: %v", keys)
	kvs, err := fetchValuesFromAPI(keys)
	if err != nil {
		return "", err
	}
	q.Q("kvs: %v", kvs)

	// replace the keys in the command with the values from the KVstore
	for key, value := range kvs {
		command = strings.Replace(command, fmt.Sprintf(":%s", key), value, -1)
	}

	return command, nil
}

// fetchValuesFromAPI retrieves the value for a given key from the KVstore API.
func fetchValuesFromAPI(keys []string) (map[string]string, error) {

	if QC.IsRoot {
		// Get from KVStore
		return GetKVValues(keys)
	}
	// Replace with your actual API endpoint
	token, err := GetToken("admin")
	if err != nil {
		return nil, err
	}
	url := QC.RootURL + "/api/v1/kvstore?keys=" + strings.Join(keys, ",")
	q.Q("fetching keys from api: ", url)
	resp, err := GetWithToken(url, token)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	// Check for a successful response
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status: %s", resp.Status)
	}
	type response struct {
		Data  map[string]string `json:"data"`
		Error string            `json:"error"`
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	q.Q("response: ", string(body))
	var result response
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}
	if result.Error != "" {
		return nil, fmt.Errorf("API returned error: %s", result.Error)
	}

	return result.Data, nil
}

// validKeyPattern is the regex pattern for validating keys
var validKeyPattern = regexp.MustCompile(`^[A-Za-z][A-Za-z0-9_-]*$`)

// HandleGetKVValue GET /kvstore/:key
// GET /kvstore/:key
// Response body example:
// {"key":"key1","value":"value1","error":""}
func HandleGetKVValue(w http.ResponseWriter, r *http.Request) {
	// Extract the key from the URL parameters
	key := chi.URLParam(r, "key")
	if key == "" {
		http.Error(w, "Key not specified", http.StatusBadRequest)
		return
	}

	// Remove colon if present
	if key[0] == ':' {
		key = key[1:]
	}

	// Validate the key against the regex pattern
	if !validKeyPattern.MatchString(key) {
		http.Error(w, "Invalid key format. Key must start with an English letter and contain only letters, numbers, hyphens, and underscores.", http.StatusBadRequest)
		return
	}

	type response struct {
		Key   string `json:"key"`
		Value string `json:"value"`
		Error string `json:"error"`
	}

	if value, ok := KVStore[key]; ok {
		json.NewEncoder(w).Encode(response{Key: key, Value: value})
	} else {
		json.NewEncoder(w).Encode(response{Error: "Key not found", Key: key})
	}

}

// HandleKVStoreBatch GET /kvstore?keys=key1,key2
// GET /kvstore?keys=key1,key2
// keys can be in format [key1,key2] or key1,key2
// Response body example:
// {"data":{"key1":"value1","key2":"value2"},"error":""}
func HandleKVStoreBatch(w http.ResponseWriter, r *http.Request) {
	// Get the 'keys' query parameter
	keysParam := r.URL.Query().Get("keys")
	if keysParam == "" {
		http.Error(w, "Keys parameter is required", http.StatusBadRequest)
		return
	}

	// Trim the square brackets if present
	keysParam = strings.Trim(keysParam, "[]")

	// Split the keys by comma
	keysList := strings.Split(keysParam, ",")

	type response struct {
		Data  map[string]string `json:"data"`
		Error string            `json:"error"`
	}
	q.Q("getting keys: %v", keysList)
	data, err := GetKVValues(keysList)
	q.Q("data: ", data, "error: ", err)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	err = json.NewEncoder(w).Encode(response{Data: data})
	if err != nil {
		q.Q(err)
		return
	}

}

// HandlePostKVStore handles the KVStore API requests.
// The request body should be a JSON object containing key-value pairs.
// Request body example:
// {"key1":"value1","key2":"value2"}
// Response body example:
// {"data":{"key1":"value1","key2":"value2"},"error":""}
func HandlePostKVStore(w http.ResponseWriter, r *http.Request) {
	var request map[string]string
	err := json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	SetKVValues(request)

	w.WriteHeader(http.StatusOK)
	type response struct {
		Data  map[string]string `json:"data"`
		Error string            `json:"error"`
	}

	json.NewEncoder(w).Encode(response{Data: request})

}

// HandleExportKVStore exports the entire KVStore
// Response body example:
// {"key1":"value1","key2":"value2"}
func HandleExportKVStore(w http.ResponseWriter, r *http.Request) {
	KVStoreMutex.Lock()
	defer KVStoreMutex.Unlock()

	jsonBytes, err := json.Marshal(KVStore)
	if err != nil {
		RespondWithError(w, err)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}
