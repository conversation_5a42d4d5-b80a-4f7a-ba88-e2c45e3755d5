package mnms

import (
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/golang-module/carbon/v2"
	"github.com/influxdata/go-syslog/v3"
	"github.com/qeof/q"
)

var SkipVerificationWords []string = []string{
	"get", "openvpn", "ipsec", "firmware", "reset", "beep",
}

var SkipVerificationCmds []string = []string{
	"mtderase", "anomaly", "idps", "opcua", "switch", "msg", "firewall", "wg", "tcpproxy",
}

func init() {
	QC.VerifyInterval = 10
}

func ShouldSkipVerification(cmd string) bool {
	for _, c := range SkipVerificationWords {
		if strings.Contains(cmd, c) {
			return true
		}
	}
	for _, c := range SkipVerificationCmds {
		if strings.HasPrefix(cmd, c) {
			return true
		}
	}
	return false
}

func RunVerifyCommands() {
	q.Q("Verify Commands running")
	for {
		QC.CmdMutex.Lock()
		cmdData := QC.CmdData
		QC.CmdMutex.Unlock()

		for key, cmdinfo := range cmdData {
			if cmdinfo.Status == "ok" && cmdinfo.Verify == "" {
				err := VerifyCommand(key, &cmdinfo)
				if err != nil {
					q.Q(err)
				}
			}
		}
		time.Sleep(time.Duration(QC.VerifyInterval) * time.Second)
	}
}

func VerifyCommand(key string, cmdinfo *CmdInfo) error {
	verified := 0
	// vertify command : snmp
	if strings.HasPrefix(cmdinfo.Command, "snmp") {
		err := verifySnmpCmds(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : device
	if strings.HasPrefix(cmdinfo.Command, "device delete") {
		err := verifyDeviceDeleteCmds(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : mqtt pub/sub/unsub
	if strings.HasPrefix(cmdinfo.Command, "mqtt pub") ||
		strings.HasPrefix(cmdinfo.Command, "mqtt sub") ||
		strings.HasPrefix(cmdinfo.Command, "mqtt unsub") {
		err := verifyMqttCmds(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : scan gwd
	if strings.HasPrefix(cmdinfo.Command, "scan gwd") || strings.HasPrefix(cmdinfo.Command, "scan snmp") {
		err := verifyScanCmds(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : config user
	if strings.HasPrefix(cmdinfo.Command, "config user") {
		err := verifyDeviceUser(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : config syslog
	if strings.HasPrefix(cmdinfo.Command, "config local syslog") {
		err := verifyLocalSyslogSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : debug log
	if strings.HasPrefix(cmdinfo.Command, "debug log") {
		err := verifyDebugLogSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : syslog
	if strings.HasPrefix(cmdinfo.Command, "syslog") {
		err := verifySyslogSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : gwd config network set
	if strings.HasPrefix(cmdinfo.Command, "gwd config network set") {
		err := verifyGwdNetworkSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : ssh tunnel
	if strings.HasPrefix(cmdinfo.Command, "ssh tunnel") || strings.HasPrefix(cmdinfo.Command, "ssh tunnels") {
		err := verifySSHSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent ssh reverse
	if strings.HasPrefix(cmdinfo.Command, "agent ssh reverse") {
		err := verifyAgentSSHSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent config syslog set
	if strings.HasPrefix(cmdinfo.Command, "agent config syslog set") {
		err := verifyAgentSyslogSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent snmp trap add/del
	if strings.HasPrefix(cmdinfo.Command, "agent snmp trap add") ||
		strings.HasPrefix(cmdinfo.Command, "agent snmp trap del") {
		err := verifyAgentTrapSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent config network set
	if strings.HasPrefix(cmdinfo.Command, "agent config network set") {
		err := verifyAgentNetworkSetting(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent snmp enable
	if strings.HasPrefix(cmdinfo.Command, "agent snmp enable") {
		err := verifyAgentSNMPEnable(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent devinfo send
	if strings.HasPrefix(cmdinfo.Command, "agent devinfo send") {
		err := verifyAgentUpdateInfoCmds("devinfo", cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent topologyinfo send
	if strings.HasPrefix(cmdinfo.Command, "agent topologyinfo send") {
		err := verifyAgentUpdateInfoCmds("topologyinfo", cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent portpwinfo send
	if strings.HasPrefix(cmdinfo.Command, "agent portpwinfo send") {
		err := verifyAgentUpdateInfoCmds("portpwinfo", cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent config port enable
	if strings.HasPrefix(cmdinfo.Command, "agent config port enable") {
		err := verifyAgentPortEnable(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent config gps enable
	if strings.HasPrefix(cmdinfo.Command, "agent config gps enable") {
		err := verifyAgentGPSEnable(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}
	// vertify command : agent config user
	if strings.HasPrefix(cmdinfo.Command, "agent config user") {
		err := verifyAgentUserCmds(cmdinfo)
		if err != nil {
			return err
		}
		verified = 1
	}

	// some other case auto verification ok
	// vertify command : reset/beep/get/firmware/mtderase command
	if ShouldSkipVerification(cmdinfo.Command) {
		cmdinfo.Verify = "ok"
		verified = 1
	}

	if verified == 1 {
		q.Q(cmdinfo.Verify)
		cmddata := make(map[string]CmdInfo)
		cmddata[key] = *cmdinfo
		QC.CmdMutex.Lock()
		QC.CmdData[key] = cmddata[key]
		QC.CmdMutex.Unlock()
	}
	return nil
}

func verifyAgentSyslogSetting(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// example : agent config syslog set AA-BB-CC-DD-EE-FF 1 ************* 5514 8 1
	ws := strings.Split(cmd, " ")
	if len(ws) < 10 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()

	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	if devdataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	syslogLogToServer := ws[5]
	syslogServerIp := ws[6]
	syslogServerPort := ws[7]
	syslogServerLevel := ws[8]
	syslogLogToFlash := ws[9]
	errorString := ""

	if syslogLogToServer != devdata.SyslogSetting.LogToServer {
		errorString += " LogToServer"
	}
	if syslogServerIp != devdata.SyslogSetting.ServerIp {
		errorString += " ServerIp"
	}
	if syslogServerPort != devdata.SyslogSetting.ServerPort {
		errorString += " ServerPort"
	}
	if syslogServerLevel != devdata.SyslogSetting.LogLevel {
		errorString += " LogLevel"
	}
	if syslogLogToFlash != devdata.SyslogSetting.LogToFlash {
		errorString += " LogToFlash"
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}
	return nil
}

func verifyAgentTrapSetting(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// example : agent snmp trap add/del AA-BB-CC-DD-EE-FF ************* 5162 public
	ws := strings.Split(cmd, " ")
	if len(ws) < 8 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()

	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	if devdataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	trapOption := ws[3]
	trapServerIp := ws[5]
	trapServerPort := ws[6]
	trapCommunity := ws[7]
	errorString := ""

	if trapOption == "add" {
		found := 0
		for _, j := range devdata.TrapSetting {
			if trapServerIp == j.ServerIp && trapServerPort == j.ServerPort &&
				trapCommunity == j.Community {
				found = 1
				break
			}
		}
		if found != 1 {
			errorString = " snmp trap"
		}
	}
	if trapOption == "del" {
		found := 0
		for _, j := range devdata.TrapSetting {
			if trapServerIp == j.ServerIp && trapServerPort == j.ServerPort &&
				trapCommunity == j.Community {
				found = 1
				break
			}
		}
		if found == 1 {
			errorString = " snmp trap"
		}
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyAgentNetworkSetting(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// example : agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0
	ws := strings.Split(cmd, " ")
	if len(ws) < 10 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()

	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	if devdataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	ipAddress := ws[5]
	netmask := ws[6]
	gateway := ws[7]
	hostname := ws[8]
	dhcp := ws[9]
	errorString := ""

	if dhcp == "1" {
		if !devdata.IsDHCP {
			errorString += " dhcp"
		}
	}
	if dhcp == "0" {
		if devdata.IsDHCP {
			errorString += " dhcp"
		}
		if ipAddress != devdata.IPAddress {
			errorString += " ip address"
		}
		if netmask != devdata.Netmask {
			errorString += " Netmask"
		}
		if gateway != devdata.Gateway {
			errorString += " gateway"
		}
	}
	if hostname != devdata.Hostname {
		errorString += " hostanme"
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyAgentSNMPEnable(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// example : agent snmp enable AA-BB-CC-DD-EE-FF 1
	ws := strings.Split(cmd, " ")
	if len(ws) < 5 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()

	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	if devdataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	snmpEnabled := ws[4]

	errorString := ""

	if snmpEnabled != devdata.SnmpEnabled {
		errorString += " SNMP enable"
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyGwdNetworkSetting(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example :
	// gwd config network set AA-BB-CC-DD-EE-FF 10.0.50.1 10.0.50.2 ************* 0.0.0.0 switch
	// DHCP enable case:
	// gwd config network set AA-BB-CC-DD-EE-FF 10.0.50.1 0.0.0.0 ************* 0.0.0.0 switch
	ws := strings.Split(cmd, " ")
	if len(ws) < 10 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()

	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	if devdataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	//ipAddress := ws[5]
	newIpAddress := ws[6]
	netmask := ws[7]
	gateway := ws[8]
	hostname := ws[9]
	errorString := ""

	if newIpAddress == "0.0.0.0" {
		if !devdata.IsDHCP {
			errorString += " dhcp"
		}
	} else {
		if devdata.IsDHCP {
			errorString += " dhcp"
		}
		if newIpAddress != devdata.IPAddress {
			errorString += " ip address"
		}
		if netmask != devdata.Netmask {
			errorString += " Netmask"
		}
		if gateway != devdata.Gateway {
			errorString += " gateway"
		}
	}
	if hostname != devdata.Hostname {
		errorString += " hostanme"
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyAgentUpdateInfoCmds(updatedCmd string, cmdinfo *CmdInfo) error {
	// Example : agent devinfo send AA-BB-CC-DD-EE-FF
	// Example : agent topologyinfo send AA-BB-CC-DD-EE-FF
	// Example : agent portpwinfo send AA-BB-CC-DD-EE-FF
	q.Q("verify command", cmdinfo.DevId, cmdinfo.Command)

	errorString := ""
	if updatedCmd == "devinfo" {
		QC.DevMutex.Lock()
		devdata := QC.DevData[cmdinfo.DevId]
		QC.DevMutex.Unlock()
		// compare timestamp to check device update status
		devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
		if err != nil {
			return fmt.Errorf("error: %v", err)
		}
		cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
		q.Q(devdataTimestamp, cmdinfoTimestamp)
		// device information update fastly
		if devdataTimestamp < (cmdinfoTimestamp - 10) {
			errorString = " device information send command"
		}
	}
	if updatedCmd == "topologyinfo" {
		QC.TopologyMutex.Lock()
		topologydata := QC.TopologyData[cmdinfo.DevId]
		QC.TopologyMutex.Unlock()
		// compare timestamp to check device update status
		topologydataTimestamp := int64(topologydata.LastUpdated)
		cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
		q.Q(topologydataTimestamp, cmdinfoTimestamp)
		// topology information update fastly
		if topologydataTimestamp < (cmdinfoTimestamp - 10) {
			errorString = " topology information send command"
		}
	}
	if updatedCmd == "portpwinfo" {
		QC.PortAndPowerMutex.Lock()
		ptpwinfodata := QC.PortAndPowerInfo[cmdinfo.DevId]
		QC.PortAndPowerMutex.Unlock()
		// compare timestamp to check device update status
		ptpwinfodataTimestamp, err := strconv.ParseInt(ptpwinfodata.Timestamp, 10, 64)
		if err != nil {
			return fmt.Errorf("error: %v", err)
		}
		cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
		q.Q(ptpwinfodataTimestamp, cmdinfoTimestamp)
		// port and power information update fastly
		if ptpwinfodataTimestamp < (cmdinfoTimestamp - 10) {
			errorString = " port and power information send command"
		}
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyScanCmds(cmdinfo *CmdInfo) error {
	// Example : scan gwd
	// Example : scan snmp
	q.Q("verify command", cmdinfo.DevId, cmdinfo.Command)

	errorString := ""
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()
	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	// device information update fastly
	if devdataTimestamp < (cmdinfoTimestamp - 10) {
		errorString = " device information send command"
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyAgentPortEnable(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example : agent config port enable AA-BB-CC-DD-EE-FF port1 1
	ws := strings.Split(cmd, " ")
	if len(ws) < 7 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.PortAndPowerMutex.Lock()
	ptpwinfodata := QC.PortAndPowerInfo[cmdinfo.DevId]
	QC.PortAndPowerMutex.Unlock()

	// compare timestamp to check device update status
	ptpwinfodataTimestamp, err := strconv.ParseInt(ptpwinfodata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(ptpwinfodataTimestamp, cmdinfoTimestamp)
	if ptpwinfodataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	portname := ws[5]
	enabled := ws[6]

	errorString := ""
	found := 0
	for _, k := range ptpwinfodata.PortStatus {
		if k.PortName == portname {
			found = 1
			if k.PortStatus {
				if enabled != "1" {
					errorString = " port enable command"
				}
			} else {
				if enabled == "1" {
					errorString = " port enable command"
				}
			}
			break
		}
	}
	if found == 0 {
		errorString = " port enable command"
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyAgentGPSEnable(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example : agent config gps enable AA-BB-CC-DD-EE-FF 1
	ws := strings.Split(cmd, " ")
	if len(ws) < 6 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()

	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	if devdataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	enabled := ws[5]
	errorString := ""

	if enabled != devdata.GpsInfo.Enabled {
		errorString = " GPS enable command"
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyMqttCmds(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example :
	//	mqtt pub ************:1883 topictest "this is messages."
	//	mqtt sub ************:1883 topictest
	//	mqtt unsub ************:1883 topictest
	ws := strings.Split(cmd, " ")
	if len(ws) < 4 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)

	option := ws[1]
	tcpaddr := ws[2]
	topicname := ws[3]
	errorString := ""

	if option == "pub" {
		//get token
		token, err := GetToken("admin")
		if err != nil {
			return err
		}

		// get syslog
		// in root
		rootURL := "http://localhost:27182"
		if !QC.IsRoot {
			rootURL = QC.RootURL
		}
		syslogResp, err := GetWithToken(rootURL+"/api/v1/syslogs?number=-1", token)
		if err != nil {
			return err
		}
		if syslogResp.StatusCode != 200 {
			syslogRespText, _ := io.ReadAll(syslogResp.Body)
			return fmt.Errorf("error: %v", string(syslogRespText))
		}
		if syslogResp == nil {
			return fmt.Errorf("error: %v", "response should not be nil")
		}
		defer syslogResp.Body.Close()

		var syslogRespBody []syslog.Base
		err = json.NewDecoder(syslogResp.Body).Decode(&syslogRespBody)
		if err != nil {
			return err
		}
		success := false
		for _, v := range syslogRespBody {
			if strings.Contains(*v.Message, topicname) && strings.Contains(*v.Message, tcpaddr) {
				success = true
				break
			}
		}
		if !success {
			errorString = " mqtt publish command"
		}
	}
	if option == "sub" {
		mqttMutex.Lock()
		allsubclient := mqttclient
		mqttMutex.Unlock()
		for _, k := range allsubclient.client {
			if k.tcp == tcpaddr {
				tmpTopic, ok := k.subscribeList[topicname]
				if ok {
					if !tmpTopic.inSubscribe {
						errorString = " mqtt subscribe command"
					}
				} else {
					errorString = " mqtt subscribe command"
				}
				break
			}
		}
	}
	if option == "unsub" {
		mqttMutex.Lock()
		allsubclient := mqttclient
		mqttMutex.Unlock()
		for _, k := range allsubclient.client {
			if k.tcp == tcpaddr {
				tmpTopic, ok := k.subscribeList[topicname]
				if ok {
					if tmpTopic.inSubscribe {
						errorString = " mqtt subscribe command"
					}
				}
				break
			}
		}
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyDeviceUser(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example : config user AA-BB-CC-DD-EE-FF admin :admin
	// Example : config user AA-BB-CC-DD-EE-FF admin default
	ws := strings.Split(cmd, " ")
	if len(ws) < 5 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()

	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	if devdataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	username := ws[3]
	password := ws[4]
	errorString := ""

	if username != devdata.UserName {
		errorString += " user name"
	}

	devdataPassword, err := ExpandCommandKVValue(devdata.PassWord)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	q.Q(devdataPassword)
	if password != devdataPassword {
		errorString += " password"
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyAgentUserCmds(cmdinfo *CmdInfo) error {
	// Example : agent config user add AA-BB-CC-DD-EE-FF daniel daniel admin
	// Example : agent config user edit AA-BB-CC-DD-EE-FF daniel daniel default
	// Example : agent config user del AA-BB-CC-DD-EE-FF daniel default
	cmdinfo.Verify = "ok"
	return nil
}

func verifyLocalSyslogSetting(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example : config local syslog path tmp/log
	// Example : config local syslog maxsize 100
	// Example : config local syslog compress true
	// Example : config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00 5
	// Example : config local syslog remote 122.147.151.234:5514
	// Example : config local syslog backup-after-forward true
	ws := strings.Split(cmd, " ")
	if len(ws) < 5 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	q.Q("verify command", cmd)

	option := ws[3]
	errorString := ""

	if option == "path" {
		value := ws[4]
		if value != QC.SyslogLocalPath {
			errorString += " local syslog path"
		}
	}
	if option == "maxsize" {
		value := ws[4]
		s, err := strconv.Atoi(value)
		if err != nil {
			return fmt.Errorf("error: %v", err)
		}
		if uint(s) != QC.SyslogFileSize {
			errorString += " local syslog maxsize"
		}
	}
	if option == "compress" {
		value := ws[4]
		boolValue, err := strconv.ParseBool(value)
		if err != nil {
			return fmt.Errorf("error: %v", err)
		}
		if boolValue != QC.SyslogCompress {
			errorString += " local syslog compress"
		}
	}
	if option == "remote" {
		value := ws[4]
		if value != QC.RemoteSyslogServerAddr {
			errorString += " local syslog remote server addr"
		}
	}
	if option == "backup-after-forward" {
		value := ws[4]
		boolValue, err := strconv.ParseBool(value)
		if err != nil {
			return fmt.Errorf("error: %v", err)
		}
		if boolValue != QC.SyslogBakAfterFwd {
			errorString += " local syslog back after forward command"
		}
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifyDebugLogSetting(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example : debug log on
	// Example : debug log off
	// Example : debug log pattern .*
	// Example : debug log output stderr
	// Example : debug log clear
	q.Q("verify command", cmd)

	errorString := ""

	if strings.HasPrefix(cmd, "debug log on") {
		if q.P != ".*" {
			errorString += " debug log on setting"
		}
	}
	if strings.HasPrefix(cmd, "debug log off") {
		if q.P != "" {
			errorString += " debug log off setting"
		}
	}
	if strings.HasPrefix(cmd, "debug log pattern") {
		ws := strings.Split(cmd, " ")
		if len(ws) < 4 {
			return fmt.Errorf("error: %v", "command length wrong")
		}
		pattern := ws[3]
		if pattern != q.P {
			errorString += " debug log pattern setting"
		}
	}
	if strings.HasPrefix(cmd, "debug log output") {
		ws := strings.Split(cmd, " ")
		if len(ws) < 4 {
			return fmt.Errorf("error: %v", "command length wrong")
		}
		output := ws[3]
		if output != q.O {
			errorString += " debug log output setting"
		}
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}
	return nil
}

func verifySyslogSetting(cmdinfo *CmdInfo) error {
	// Example : syslog config path tmp/log
	// Example : syslog config maxsize 100
	// Example : syslog config compress true
	// Example : syslog list
	// Example : syslog export syslog.log 2023/02/21 22:06:00 2023/02/22 22:08:00 5
	// Example : syslog rm exported
	// Example : syslog config remote www.example.com:5514
	// Example : syslog config backup-after-forward true
	// Example : syslog config severity-range-forward -1 7
	// Example : syslog config get

	// root can not get data in log service
	cmdinfo.Verify = "ok"
	return nil
}

func verifyAgentSSHSetting(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example : agent ssh reverse start AA-BB-CC-DD-EE-FF 1.2.3.4 12345 443 22
	// Example : agent ssh reverse stop AA-BB-CC-DD-EE-FF 1.2.3.4 12345
	// Example : agent ssh reverse status AA-BB-CC-DD-EE-FF
	// Example : agent ssh reverse websrv AA-BB-CC-DD-EE-FF
	q.Q("verify command", cmdinfo.DevId, cmd)
	QC.DevMutex.Lock()
	devdata := QC.DevData[cmdinfo.DevId]
	QC.DevMutex.Unlock()

	// compare timestamp to check device update status
	devdataTimestamp, err := strconv.ParseInt(devdata.Timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	cmdinfoTimestamp := carbon.Parse(cmdinfo.Timestamp).Timestamp()
	q.Q(devdataTimestamp, cmdinfoTimestamp)
	if devdataTimestamp < cmdinfoTimestamp {
		q.Q("wait device update information")
		return nil
	}

	errorString := ""

	// skip command "agent ssh reverse start"
	// skip command "agent ssh reverse stop"
	// skip command "agent ssh reverse status"
	if strings.HasPrefix(cmd, "agent ssh reverse websrv") {
		if devdata.TunneledUrl == "" {
			errorString += " ssh auto command"
		}
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifySSHSetting(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example : ssh tunnel close 62345
	// Example : ssh tunnel fetch 62345
	// Example : ssh tunnels list

	q.Q("verify command", cmd)

	errorString := ""

	if strings.HasPrefix(cmd, "ssh tunnel close") {
		ws := strings.Split(cmd, " ")
		if len(ws) < 4 {
			return fmt.Errorf("error: %v", "command length wrong")
		}
		listenPort, err := strconv.Atoi(ws[3])
		if err != nil {
			return err
		}

		QC.SshConnectionsMutex.Lock()
		_, ok := QC.SshConnections[listenPort]
		if ok {
			errorString += " ssh tunnel close command"
		}
		QC.SshConnectionsMutex.Unlock()
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of" + errorString + " failed"
	}

	return nil
}

func verifySnmpCmds(cmdinfo *CmdInfo) error {
	cmdinfo.Verify = "ok"
	return nil
}

func verifyDeviceDeleteCmds(cmdinfo *CmdInfo) error {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return fmt.Errorf("error: %v", err)
	}
	// Example : device delete all/AA-BB-CC-DD-EE-FF
	q.Q("verify command", cmd)

	ws := strings.Split(cmd, " ")
	if len(ws) < 3 {
		return fmt.Errorf("error: %v", "command length wrong")
	}
	target := ws[2]
	errorString := ""

	if target == "all" {
		QC.DevMutex.Lock()
		if len(QC.DevData) > 1 {
			errorString += "delete device list"
		}
		QC.DevMutex.Unlock()
	} else {
		QC.DevMutex.Lock()
		_, ok := QC.DevData[target]
		if ok {
			errorString += "delete " + target
		}
		QC.DevMutex.Unlock()
	}

	if errorString == "" {
		cmdinfo.Verify = "ok"
	} else {
		cmdinfo.Verify = "error: Verification of " + errorString + " failed"
	}

	return nil
}
