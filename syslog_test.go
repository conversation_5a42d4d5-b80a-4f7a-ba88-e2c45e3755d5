package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"os/signal"
	"path"
	"path/filepath"
	"runtime"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/influxdata/go-syslog/v3"
	"github.com/qeof/q"
)

func killNmsctlProcesses() {
	var cmd *exec.Cmd
	q.Q("killing bb processes")
	// XXX terrible dangerous killing of all mnmsctl
	// root service
	services := []string{"bbrootsvc", "bbnmssvc", "bblogsvc", "bbidpsvc"}
	for _, service := range services {
		if runtime.GOOS == "windows" {
			cmd = exec.Command("taskkill", "/f", "/im", service+".exe")
		} else {
			cmd = exec.Command("killall", service)
		}
		cmd.Run()
	}
}

func TestSyslog(t *testing.T) {
	//RFC 3164 Page 10, Facility=20 and Severity=5 would have Priority value of 165
	syslogMsg := []byte("<165>Nov 11 12:34:56 myhost mytag: this is a syslog message")

	facility, severity, err := SyslogParsePriority(string(syslogMsg))
	if err != nil {
		t.Fatal(err)
	}
	q.Q(facility, severity)
	if facility != 20 {
		t.Fatal("facility is wrong")
	}
	if severity != 5 {
		t.Fatal("severity is wrong")
	}

	//logger -d -s -n localhost -P 5514 --rfc3164 -p local3.alert local3 alert syslog test
	//<153>Feb 12 02:23:21 cs-186432676255-default bob_bae: local3 alert syslog test

	QC.RemoteSyslogServerAddr = "localhost:5514"
	err = SendSyslog(LOG_NOTICE, "testsyslog", "this is a test alert syslog message")
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		killNmsctlProcesses()
	}()
	go func() {
		cmd := exec.Command("./bbrootsvc/bbrootsvc", "-n", "root", "-O", "root.log")
		err := cmd.Run()
		if err != nil {
			q.Q(err)
		}
	}()
	err = waitForRoot()
	if err != nil {
		q.Q(err)
		t.Fatal(err)
	}
	go func() {
		for {
			time.Sleep(1 * time.Second)
			q.Q("sending syslog alert for websock")
			err = SendSyslog(LOG_NOTICE, "syslog_test", "websock test alert syslog message")
			if err != nil {
				q.Q("Fatal", err)
				break
			}
			q.Q("sent syslog alert for websock")
		}
	}()
	connectWebsock()
}

func connectWebsock() {
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)
	u := url.URL{Scheme: "ws", Host: "localhost:27182", Path: "/api/v1/ws"}
	q.Q(u.String())
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		q.Q(err)
		return
	}
	defer c.Close()
	done := make(chan struct{})
	go func() {
		defer close(done)
		for {
			_, message, err := c.ReadMessage()
			if err != nil {
				q.Q(err)
				return
			}
			q.Q("read websock", string(message))
		}
	}()
	ticker := time.NewTicker(time.Second)
	count := 1
	defer ticker.Stop()
	for {
		select {
		case <-done:
			return
		case t := <-ticker.C:
			msg := fmt.Sprintf(`{ "kind": "syslog_test", "level": 3, "message": "ticker %v" }`, t)
			count++
			if count > 3 {
				return
			}
			err := c.WriteMessage(websocket.TextMessage, []byte(msg))
			if err != nil {
				q.Q(err)
				return
			}
			q.Q("wrote to websock", msg)
		case <-interrupt:
			q.Q("interrupt")
			err := c.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, "closing at interrupt"))
			if err != nil {
				q.Q(err)
				return
			}
			select {
			case <-done:
			case <-time.After(time.Second):
			}
			return
		}
	}
}

func TestSyslogFromRealDevice(t *testing.T) {
	defer func() {
		killNmsctlProcesses()
	}()
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbrootsvc/bbrootsvc.exe", "-n", "root")
		} else {
			cmd = exec.Command("./bbrootsvc/bbrootsvc", "-n", "root")
		}
		err := cmd.Run()
		if err != nil {
			q.Q(err)
		}
	}()
	// wait root
	time.Sleep(3 * time.Second)
	// network service
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbnmssvc/bbnmssvc.exe", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		} else {
			cmd = exec.Command("./bbnmssvc/bbnmssvc", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		}
		err := cmd.Run()
		if err != nil {
			q.Q(err)
		}
	}()
	// wait root
	time.Sleep(10 * time.Second)

	// start
	RootURL := "http://localhost:27182"

	ifaceaddrs, err := IfnetAddresses()
	if err != nil {
		t.Fatalf("IfnetAddresses %v", err)
	}

	//login
	loginurl := RootURL + "/api/v1/login"
	jsonBytes, err := json.Marshal(map[string]string{
		"user":     "admin",
		"password": "default",
	})
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	resp, err := http.Post(loginurl, "application/json", bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	if resp.StatusCode != http.StatusOK {
		resText, _ := io.ReadAll(resp.Body)
		t.Fatalf("error: %v\n", string(resText))
		return
	}
	if resp == nil {
		t.Fatalf("error: %v\n", "response should not be nil")
		return
	}
	defer resp.Body.Close()

	//get token
	var respBody map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	token, ok := respBody["token"].(string)
	if !ok {
		t.Fatalf("error: %v\n", "token is not string")
		return
	}

	// get device list
	devicesurl := RootURL + "/api/v1/devices"
	devcieResp, err := GetWithToken(devicesurl, token)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	if devcieResp.StatusCode != 200 {
		deviceRespText, _ := io.ReadAll(devcieResp.Body)
		t.Fatalf("error: %v\n", string(deviceRespText))
		return
	}
	if devcieResp == nil {
		t.Fatalf("error: %v\n", "response should not be nil")
		return
	}
	defer devcieResp.Body.Close()

	var devcieRespBody map[string]DevInfo
	err = json.NewDecoder(devcieResp.Body).Decode(&devcieRespBody)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	var deviceNumber int
	for k, _ := range devcieRespBody {
		if k != "11-22-33-44-55-66" {
			deviceNumber = deviceNumber + 1
			//t.Logf("%-18s %-18s %-18s %-18s\n", v.IPAddress, v.Mac, v.ModelName, v.Hostname)
		}
	}
	//t.Logf("Device number : %d\n", deviceNumber)

	// test syslog
	//SyslogServerArray := strings.Split(SyslogServer, ":")
	//SyslogServerIP := SyslogServerArray[0]
	//SyslogServerPort := SyslogServerArray[1]
	SyslogServerIP := ""
	SyslogServerPort := "5514"
	TestDeviceIP := ""
	TestDeviceMac := ""

	// select syslog
	select_device := false
	for k, v := range devcieRespBody {
		if k != "11-22-33-44-55-66" {
			if strings.Contains(v.ModelName, "EHG7508") {
				for _, strip := range ifaceaddrs {
					ip := net.ParseIP(v.IPAddress)
					_, ipNet, _ := net.ParseCIDR(strip + "/24")
					if ipNet.Contains(ip) {
						// v.IPAddres in SyslogServerIP/24
						select_device = true
						TestDeviceMac = v.Mac
						TestDeviceIP = v.IPAddress
						SyslogServerIP = strip
						break
					}
				}
				if select_device {
					break
				}
			}
		}
	}
	if !select_device {
		t.Logf("%s\n", "Pass: not found EHG7508 device, pass syslog test.")
		return
	}

	//t.Log("\nTest device :\n")
	//t.Logf("%-18s : %-18s\n", "IP address", TestDeviceIP)
	//t.Logf("%-18s : %-18s\n", "Mac address", TestDeviceMac)
	//t.Logf("%-18s : %-18s\n", "Syslog server IP", SyslogServerIP)
	//t.Logf("%-18s : %-18s\n", "Syslog server Mac", SyslogServerPort)
	//t.Logf("\n")
	// enable snmp
	//t.Logf("%s\n", "Process : enable snmp")
	PostCmd(RootURL, token, "snmp enable %s")
	time.Sleep(3 * time.Second)

	cmd := fmt.Sprintf("snmp update community %s public private", TestDeviceMac)
	PostCmd(RootURL, token, cmd)
	time.Sleep(5 * time.Second)

	// set syslog
	//t.Logf("%s\n", "Process : set syslog")
	//config syslog set [MAC Address] [Enable] [Server IP] [Server Port] [Log Level] [Log to Flash]
	cmd = fmt.Sprintf("config syslog set %s 1 %s %s 1 1", TestDeviceMac, SyslogServerIP, SyslogServerPort)
	PostCmd(RootURL, token, cmd)
	time.Sleep(3 * time.Second)

	// reset device and device will tranfer syslog
	//t.Logf("%s\n", "Process : reset device")
	cmd = fmt.Sprintf("reset %s", TestDeviceMac)
	PostCmd(RootURL, token, cmd)
	time.Sleep(3 * time.Second)

	//t.Logf("%s\n", "Process : Wait 20 seconds")
	time.Sleep(20 * time.Second)
	// get syslog
	//t.Logf("%s\n", "Process : get syslog")
	syslogURL := RootURL + "/api/v1/syslogs?number=10"
	syslogResp, err := GetWithToken(syslogURL, token)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	if syslogResp.StatusCode != 200 {
		syslogRespText, _ := io.ReadAll(syslogResp.Body)
		t.Fatalf("error: %v\n", string(syslogRespText))
		return
	}
	if syslogResp == nil {
		t.Fatalf("error: %v\n", "response should not be nil")
		return
	}
	defer syslogResp.Body.Close()

	var syslogRespBody []syslog.Base
	err = json.NewDecoder(syslogResp.Body).Decode(&syslogRespBody)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	success := false
	for _, v := range syslogRespBody {
		if strings.Contains(*v.Message, "syslog") && !strings.Contains(*v.Message, "command") && strings.Contains(*v.Message, TestDeviceIP) {
			t.Logf("Message : %v\n", *v.Message)
			success = true
			break
		}
	}
	if success {
		t.Logf("Result : Device can transfer syslog to root.\n")
	} else {
		t.Logf("Result : Device can not transfer syslog to root.\n")
	}
	// Restore syslog
	//t.Logf("\n%s\n", "Process : Restore syslog")
	cmd = fmt.Sprintf("config syslog set %s 2 0.0.0.0 514 3 2", TestDeviceMac)
	PostCmd(RootURL, token, cmd)
}

func Test_SyslogList(t *testing.T) {
	fileName := filepath.Base(QC.SyslogLocalPath)
	fileNamePrefix := strings.TrimSuffix(fileName, filepath.Ext(fileName))
	fileNamePostfix := strings.TrimPrefix(fileName, fileNamePrefix)

	// Generate fake syslog files for network management system testing
	// Format: prefix-YYYY-MM-DDTHH-MM-SS.mmm.suffix
	genFiles := []string{
		fmt.Sprintf("%s-2024-01-22T11-52-48.492%s", fileNamePrefix, fileNamePostfix),
		fmt.Sprintf("%s-2024-02-22T11-52-48.492%s", fileNamePrefix, fileNamePostfix),
		fmt.Sprintf("%s-2024-03-22T11-52-48.492%s.gz", fileNamePrefix, fileNamePostfix),
	}

	// Create test syslog files with network management content
	for _, f := range genFiles {
		content := fmt.Sprintf("<134>%s network-device-01: Interface GigabitEthernet0/1 status changed to up",
			time.Now().Format("Jan 02 15:04:05"))
		err := os.WriteFile(f, []byte(content), 0644)
		if err != nil {
			t.Fatal(err)
		}
		defer os.Remove(f)
	}

	cmdinfo := CmdInfo{
		Kind:    "syslog",
		Command: "syslog list",
	}
	ret := SyslogListLogFilesCmd(&cmdinfo)
	q.Q(ret)

	// Parse the JSON response for network management system syslog files
	var listResult struct {
		Files      []string `json:"files"`
		ExportUrls []string `json:"export_urls"`
	}
	err := json.Unmarshal([]byte(ret.Result), &listResult)
	if err != nil {
		t.Fatal(err)
	}
	files := listResult.Files

	// Verify that compressed (.gz) files are excluded from syslog list
	// This is important for network management system log rotation
	for _, f := range files {
		if strings.HasSuffix(f, ".gz") {
			t.Fatalf("compressed file %s should not be in syslog list for network management", f)
		}
	}

	// Verify that our non-compressed test files are included in the syslog list
	expectedFiles := []string{
		fmt.Sprintf("%s-2024-01-22T11-52-48.492%s", fileNamePrefix, fileNamePostfix),
		fmt.Sprintf("%s-2024-02-22T11-52-48.492%s", fileNamePrefix, fileNamePostfix),
	}

	for _, expectedFile := range expectedFiles {
		found := slices.Contains(files, expectedFile)
		if !found {
			t.Fatalf("expected network management syslog file %s not found in list", expectedFile)
		}
	}

	// Verify the export URLs structure for network management system integration
	if len(listResult.ExportUrls) > 0 {
		for _, url := range listResult.ExportUrls {
			if !strings.Contains(url, "/api/v1/files/syslogs/") {
				t.Errorf("export URL %s does not follow expected network management API pattern", url)
			}
		}
	}

	t.Logf("Network management syslog list test completed successfully with %d files and %d export URLs",
		len(files), len(listResult.ExportUrls))
}

func Test_SyslogExport(t *testing.T) {
	QC.NmsServiceURL = "http://localhost:27192"
	oldPath := QC.SyslogLocalPath
	QC.SyslogLocalPath = "testsyslog.log"
	defer func() {
		QC.SyslogLocalPath = oldPath
	}()

	// create a file with some fake syslog content
	c := []string{}
	for i := 0; i < 100; i++ {
		c = append(c, fmt.Sprintf("<1>Jan  3 16:01:53 host GoTest: new line: %d", i))
	}
	err := os.WriteFile(QC.SyslogLocalPath, []byte(strings.Join(c, "\n")), 0644)
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(QC.SyslogLocalPath)

	cmdinfo := CmdInfo{
		Kind:    "syslog",
		Command: "syslog export testsyslog.log 10",
	}

	ret := SyslogGetUrlCmd(&cmdinfo)
	retUrl := ret.Result
	q.Q(retUrl)
	// get filename from url
	u, err := url.Parse(retUrl)
	if err != nil {
		t.Fatal(err)
	}
	filename := filepath.Base(u.Path)

	path, err := EnsureStaticFilesFolderExist()
	if err != nil {
		t.Fatal(err)
	}
	path = filepath.Join(path, "syslogs", filename)

	// check file content
	b, err := os.ReadFile(path)
	if err != nil {
		t.Fatal(err)
	}
	lines := strings.Split(string(b), "\n")
	if len(lines) != 10 {
		t.Fatalf("expected 10 lines, got %d", len(lines))
	}
}

func Test_GetSyslogWithParameters(t *testing.T) {
	// create a file with some fake syslog content
	oldPath := QC.SyslogLocalPath
	QC.SyslogLocalPath = "testsyslog.log"
	defer func() {
		QC.SyslogLocalPath = oldPath
	}()

	// create old file first
	c_prevDay := []string{}
	for i := 0; i < 60; i++ {
		c_prevDay = append(c_prevDay, fmt.Sprintf("<1>Jan  2 16:%02d:53 host old: new line: %d", i, i))
	}
	err := os.WriteFile("testsyslog-2024-01-02T11-52-48.492.log", []byte(strings.Join(c_prevDay, "\n")), 0644)
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove("testsyslog-2024-01-02T11-52-48.492.log")

	// create new file
	c := []string{}
	for i := 0; i < 60; i++ {
		c = append(c, fmt.Sprintf("<1>Jan  3 16:%02d:53 host new: new line: %d", i, i))
	}
	err = os.WriteFile(QC.SyslogLocalPath, []byte(strings.Join(c, "\n")), 0644)
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(QC.SyslogLocalPath)

	// getSyslogWithTime
	// get this year
	year := time.Now().Year()
	start := fmt.Sprintf("%d/01/03 16:00:00", year)
	end := fmt.Sprintf("%d/01/03 16:10:00", year)
	logs, err := getSyslogsWithTime([]string{"testsyslog.log"}, start, end, 5)
	if err != nil {
		t.Fatal(err)
	}
	if len(logs) != 5 {
		t.Fatalf("expected 5 lines, got %d", len(logs))
	}

	// getSyslogWithLast 5
	logs, err = getSyslogsWithLast([]string{"testsyslog.log"}, 5)
	if err != nil {
		t.Fatal(err)
	}
	if len(logs) != 5 {
		t.Fatalf("expected 5 lines, got %d", len(logs))
	}
	for i, log := range logs {
		msg := *log.Message
		idx := 55 + i
		if msg != "new line: "+fmt.Sprint(idx) {
			t.Fatalf("expected new line: %d, got %s", idx, msg)
		}
	}

	// getSyslogWithLast 2 files 100
	logs, err = getSyslogsWithLast([]string{"testsyslog.log", "testsyslog-2024-01-02T11-52-48.492.log"}, 100)
	if err != nil {
		t.Fatal(err)
	}
	if len(logs) != 100 {
		t.Fatalf("expected 100 lines, got %d", len(logs))
	}
	// should get 20-59 old and 0-59 new
	for i, log := range logs {
		if i < 40 {
			msg := *log.Message
			idx := 20 + i
			if msg != "new line: "+fmt.Sprint(idx) {
				t.Fatalf("expected new line: %d, got %s", idx, msg)
			}
		} else {
			msg := *log.Message
			idx := i - 40
			if msg != "new line: "+fmt.Sprint(idx) {
				t.Fatalf("expected new line: %d, got %s", idx, msg)
			}
		}
	}
}

func Test_SyslogRemove(t *testing.T) {
	// test syslog rm syslog
	// should be error due to not support
	QC.Kind = "syslog"
	cmdinfo := CmdInfo{Command: "syslog rm syslog"}
	cmdinfo = *RunCmd(&cmdinfo)
	if !strings.Contains(cmdinfo.Status, "error:") {
		t.Fatalf("expected error status, got %s", cmdinfo.Status)
	}

	// test syslog rm exported
	// add fake file in /files/syslogs
	fileDir, err := EnsureStaticFilesFolderExist()
	if err != nil {
		q.Q(err)
		return
	}
	fileDir = path.Join(fileDir, "syslogs")
	// mkdir if not exist
	err = os.MkdirAll(fileDir, 0755)
	if err != nil {
		q.Q(err)
		return
	}

	filename := "syslog_test_10.log"
	err = os.WriteFile(path.Join(fileDir, filename), []byte("test"), 0644)
	if err != nil {
		t.Fatal(err)
	}

	cmdinfo = CmdInfo{Command: "syslog rm exported syslog_test_10.log"}
	cmdinfo = *RunCmd(&cmdinfo)

	t.Log(cmdinfo)

	// check if the file is removed
	if _, err := os.Stat(path.Join(fileDir, filename)); !os.IsNotExist(err) {
		defer os.Remove(path.Join(fileDir, filename))
		t.Fatalf("file %s should be removed", filename)
	}

	// test file not exist, should return error
	cmdinfo = CmdInfo{Command: "syslog rm exported not_exist.log"}
	cmdinfo = *RunCmd(&cmdinfo)
	if !strings.Contains(cmdinfo.Status, "error:") {
		t.Fatalf("expected error status, got %s", cmdinfo.Status)
	}
}

func Test_SyslogSeverityForward(t *testing.T) {
	oldSeverityRangeForward := QC.SyslogSeverityRngFwd
	QC.SyslogSeverityRngFwd.MinSeverity = -1
	QC.SyslogSeverityRngFwd.MaxSeverity = -1

	message := "<165>Feb 12 02:23:21 cs-186432676255-default bob_bae: local3 alert syslog test"
	_, severity, err := SyslogParsePriority(message)
	if err != nil {
		t.Fatal(err)
	}

	inRange := checkSeverityRange(severity)
	if inRange {
		t.Fatal("severity should not be in range")
	}

	QC.SyslogSeverityRngFwd.MinSeverity = 0
	QC.SyslogSeverityRngFwd.MaxSeverity = 7
	inRange = checkSeverityRange(severity)
	if !inRange {
		t.Fatal("severity should be in range")
	}

	QC.SyslogSeverityRngFwd = oldSeverityRangeForward
}

/*

// ----------------------config local syslog path [path]-------------------------------------
// Setup and teardown
func setup() {
	QC = QContext{
		SyslogLocalPath:        "",  // Reset global config
		SyslogFileSize:         100, // megabytes
		SyslogCompress:         true,
		SyslogBakAfterFwd:      false,
		SyslogSeverityRngFwd:   SyslogSeverityRange{0, 7},
		SyslogServerAddr:       ":5514", // ":514"
		RemoteSyslogServerAddr: "",
	}
}

func TestSyslogSetPathCmd_ValidCommand(t *testing.T) {
	setup()
	cmd := "config local syslog path /var/log/syslog"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "/var/log/syslog" {
		t.Errorf("Expected path '/var/log/syslog', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_MinimumValidPath(t *testing.T) {
	setup()
	cmd := "config local syslog path a"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "a" {
		t.Errorf("Expected path 'a', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_PathWithSpaces(t *testing.T) {
	setup()
	cmd := "config local syslog path /var/log/my syslog"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if !strings.Contains(result.Status, "too many arguments") {
		t.Errorf("Expected 'too many arguments' error, got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "" {
		t.Errorf("Expected path to remain unchanged, got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_TooFewArguments(t *testing.T) {
	setup()
	cmd := "config local syslog"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

func TestSyslogSetPathCmd_TooManyArguments(t *testing.T) {
	setup()
	cmd := "config local syslog path /var/log/syslog extra"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if !strings.Contains(result.Status, "too many arguments") {
		t.Errorf("Expected 'too many arguments' error, got '%s'", result.Status)
	}
}

func TestSyslogSetPathCmd_EmptyPath(t *testing.T) {
	setup()
	cmd := "config local syslog path \"\""
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "" {
		t.Errorf("Expected empty path, got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_SpecialCharacters(t *testing.T) {
	setup()
	cmd := "config local syslog path /var/log/@syslog_2023!"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "/var/log/@syslog_2023!" {
		t.Errorf("Expected path '/var/log/@syslog_2023!', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_RelativePath(t *testing.T) {
	setup()
	cmd := "config local syslog path ../relative/path"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "../relative/path" {
		t.Errorf("Expected path '../relative/path', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_LongPath(t *testing.T) {
	setup()
	longPath := "/a/very/long/path/" + strings.Repeat("x", 300)
	cmd := "config local syslog path " + longPath
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != longPath {
		t.Errorf("Expected long path, got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_MissingPathArgument(t *testing.T) {
	setup()
	cmd := "config local syslog path"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

func TestSyslogSetPathCmd_UnicodePath(t *testing.T) {
	setup()
	cmd := "config local syslog path /var/log/日本語"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "/var/log/日本語" {
		t.Errorf("Expected path '/var/log/日本語', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_EnvironmentVariables(t *testing.T) {
	setup()
	cmd := "config local syslog path $HOME/logs"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "$HOME/logs" {
		t.Errorf("Expected path '$HOME/logs', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_TrailingSlash(t *testing.T) {
	setup()
	cmd := "config local syslog path /var/log/"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "/var/log/" {
		t.Errorf("Expected path '/var/log/', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_MultipleSlashes(t *testing.T) {
	setup()
	cmd := "config local syslog path /var////log"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "/var////log" {
		t.Errorf("Expected path '/var////log', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_ControlCharacters(t *testing.T) {
	setup()
	cmd := "config local syslog path /var/log/\t\n"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogLocalPath != "/var/log/\t\n" {
		t.Errorf("Expected path '/var/log/\\t\\n', got '%s'", QC.SyslogLocalPath)
	}
}

func TestSyslogSetPathCmd_MalformedCommand(t *testing.T) {
	setup()
	cmd := "random garbage input"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetPathCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

//-----------------------------------------config local syslog maxsize-----------------------------------------

func TestSyslogSetMaxSizeCmd_ValidInput(t *testing.T) {
	setup()
	cmd := "config local syslog maxsize 100"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogFileSize != 100 {
		t.Errorf("Expected maxsize 100, got %d", QC.SyslogFileSize)
	}
}

func TestSyslogSetMaxSizeCmd_MinimumValue(t *testing.T) {
	setup()
	cmd := "config local syslog maxsize 1"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogFileSize != 1 {
		t.Errorf("Expected maxsize 1, got %d", QC.SyslogFileSize)
	}
}

func TestSyslogSetMaxSizeCmd_MaximumValue(t *testing.T) {
	setup()
	cmd := "config local syslog maxsize 2147483647" // max int32
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogFileSize != 2147483647 {
		t.Errorf("Expected maxsize 2147483647, got %d", QC.SyslogFileSize)
	}
}

func TestSyslogSetMaxSizeCmd_ZeroValue(t *testing.T) {
	setup()
	cmd := "config local syslog maxsize 0"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	if result.Status != "ok" { // Assuming 0 is valid
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogFileSize != 0 {
		t.Errorf("Expected maxsize 0, got %d", QC.SyslogFileSize)
	}
}

func TestSyslogSetMaxSizeCmd_NonNumericInput(t *testing.T) {
	setup()
	cmd := "config local syslog maxsize abc"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetMaxSizeCmd_TooFewArguments(t *testing.T) {
	setup()
	cmd := "config local syslog maxsize"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

func TestSyslogSetMaxSizeCmd_TooManyArguments(t *testing.T) {
	setup()
	cmd := "config local syslog maxsize 100 extra"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	expected := "error: too many arguments, expected 5 but got 6"
	if result.Status != expected {
		t.Errorf("Expected '%s', got '%s'", expected, result.Status)
	}
}

func TestSyslogSetMaxSizeCmd_EmptyInput(t *testing.T) {
	setup()
	cmd := "config local syslog "
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

func TestSyslogSetMaxSizeCmd_LeadingZeros(t *testing.T) {
	setup()
	cmd := "config local syslog maxsize 0100"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetMaxSizeCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogFileSize != 100 {
		t.Errorf("Expected maxsize 100, got %d", QC.SyslogFileSize)
	}
}

func TestSyslogSetMaxSizeCmd_BoundaryValues(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected uint
		valid    bool
	}{
		{"Max uint32", "4294967295", 4294967295, true},
		{"Overflow", "4294967296", 0, false},
		{"Very large", "1000000000000", 0, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setup()
			cmd := "config local syslog maxsize " + tt.input
			cmdInfo := &CmdInfo{Command: cmd}

			result := SyslogSetMaxSizeCmd(cmdInfo)

			if tt.valid {
				if result.Status != "ok" {
					t.Errorf("Expected status 'ok', got '%s'", result.Status)
				}
				if QC.SyslogFileSize != tt.expected {
					t.Errorf("Expected maxsize %d, got %d", tt.expected, QC.SyslogFileSize)
				}
			} else {
				if !strings.Contains(result.Status, "error:") {
					t.Errorf("Expected error status, got '%s'", result.Status)
				}
			}
		})
	}
}

//-----------------------------------------config local syslog compress -----------------------------------

func TestSyslogSetCompressCmd_ValidTrue(t *testing.T) {
	setup()
	cmd := "config local syslog compress true"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if !QC.SyslogCompress {
		t.Error("Expected SyslogCompress to be true")
	}
}

func TestSyslogSetCompressCmd_ValidFalse(t *testing.T) {
	setup()
	cmd := "config local syslog compress false"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogCompress {
		t.Error("Expected SyslogCompress to be false")
	}
}

func TestSyslogSetCompressCmd_CaseInsensitiveTrue(t *testing.T) {
	setup()
	cmd := "config local syslog compress TRUE"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if !QC.SyslogCompress {
		t.Error("Expected SyslogCompress to be true")
	}
}

func TestSyslogSetCompressCmd_CaseInsensitiveFalse(t *testing.T) {
	setup()
	cmd := "config local syslog compress FALSE"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogCompress {
		t.Error("Expected SyslogCompress to be false")
	}
}

func TestSyslogSetCompressCmd_OneAsTrue(t *testing.T) {
	setup()
	cmd := "config local syslog compress 1"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if !QC.SyslogCompress {
		t.Error("Expected SyslogCompress to be true")
	}
}

func TestSyslogSetCompressCmd_ZeroAsFalse(t *testing.T) {
	setup()
	cmd := "config local syslog compress 0"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogCompress {
		t.Error("Expected SyslogCompress to be false")
	}
}

func TestSyslogSetCompressCmd_InvalidBoolean(t *testing.T) {
	setup()
	cmd := "config local syslog compress maybe"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetCompressCmd_TooFewArguments(t *testing.T) {
	setup()
	cmd := "config local syslog compress"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

func TestSyslogSetCompressCmd_TooManyArguments(t *testing.T) {
	setup()
	cmd := "config local syslog compress true extra"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	expected := "error: too many arguments, expected 5 but got 6"
	if result.Status != expected {
		t.Errorf("Expected '%s', got '%s'", expected, result.Status)
	}
}

func TestSyslogSetCompressCmd_EmptyInput(t *testing.T) {
	setup()
	cmd := "config local syslog "
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetCompressCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

func TestSyslogSetCompressCmd_YesNoValues(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
		valid    bool
	}{
		{"Yes", "yes", true, true},
		{"No", "no", false, true},
		{"Y", "y", true, true},
		{"N", "n", false, true},
		{"On", "on", true, true},
		{"Off", "off", false, true},
		{"Invalid", "invalid", false, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setup()
			cmd := "config local syslog compress " + tt.input
			cmdInfo := &CmdInfo{Command: cmd}

			result := SyslogSetCompressCmd(cmdInfo)

			if tt.valid {
				if result.Status != "ok" {
					t.Errorf("Expected status 'ok', got '%s'", result.Status)
				}
				if QC.SyslogCompress != tt.expected {
					t.Errorf("Expected %v, got %v", tt.expected, QC.SyslogCompress)
				}
			} else {
				if !strings.Contains(result.Status, "error:") {
					t.Errorf("Expected error status, got '%s'", result.Status)
				}
			}
		})
	}
}

func TestSyslogSetCompressCmd_NumericValues(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
		valid    bool
	}{
		{"Positive", "2", true, true},
		{"Negative", "-1", false, false},
		{"Decimal", "1.0", false, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setup()
			cmd := "config local syslog compress " + tt.input
			cmdInfo := &CmdInfo{Command: cmd}

			result := SyslogSetCompressCmd(cmdInfo)

			if tt.valid {
				if result.Status != "ok" {
					t.Errorf("Expected status 'ok', got '%s'", result.Status)
				}
				if QC.SyslogCompress != tt.expected {
					t.Errorf("Expected %v, got %v", tt.expected, QC.SyslogCompress)
				}
			} else {
				if !strings.Contains(result.Status, "error:") {
					t.Errorf("Expected error status, got '%s'", result.Status)
				}
			}
		})
	}
}

// -----------------------------------------config local syslog remote -----------------------------------
func TestSyslogSetRemoteCmd_ValidPortOnly(t *testing.T) {
	setup()
	cmd := "config local syslog remote :5514"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.RemoteSyslogServerAddr != ":5514" {
		t.Errorf("Expected address ':5514', got '%s'", QC.RemoteSyslogServerAddr)
	}
}

func TestSyslogSetRemoteCmd_ValidIPAndPort(t *testing.T) {
	setup()
	cmd := "config local syslog remote ***************:5514"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.RemoteSyslogServerAddr != "***************:5514" {
		t.Errorf("Expected address '***************:5514', got '%s'", QC.RemoteSyslogServerAddr)
	}
}

func TestSyslogSetRemoteCmd_ValidHostnameAndPort(t *testing.T) {
	setup()
	cmd := "config local syslog remote logs.example.com:5514"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.RemoteSyslogServerAddr != "logs.example.com:5514" {
		t.Errorf("Expected address 'logs.example.com:5514', got '%s'", QC.RemoteSyslogServerAddr)
	}
}

func TestSyslogSetRemoteCmd_IPv6Address(t *testing.T) {
	setup()
	cmd := "config local syslog remote [2001:db8::1]:5514"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.RemoteSyslogServerAddr != "[2001:db8::1]:5514" {
		t.Errorf("Expected address '[2001:db8::1]:5514', got '%s'", QC.RemoteSyslogServerAddr)
	}
}

func TestSyslogSetRemoteCmd_MissingPort(t *testing.T) {
	setup()
	cmd := "config local syslog remote ***************"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetRemoteCmd_InvalidPort(t *testing.T) {
	setup()
	cmd := "config local syslog remote ***************:notaport"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetRemoteCmd_TooFewArguments(t *testing.T) {
	setup()
	cmd := "config local syslog remote"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

func TestSyslogSetRemoteCmd_TooManyArguments(t *testing.T) {
	setup()
	cmd := "config local syslog remote :5514 extra"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	expected := "error: too many arguments, expected 5 but got 6"
	if result.Status != expected {
		t.Errorf("Expected '%s', got '%s'", expected, result.Status)
	}
}

func TestSyslogSetRemoteCmd_EmptyAddress(t *testing.T) {
	setup()
	cmd := "config local syslog remote "
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if result.Status != "error: will not forward to remote syslog, missing remote syslog server address" {
		t.Errorf("Expected 'error: will not forward to remote syslog, missing remote syslog server address', got '%s'", result.Status)
	}
}

func TestSyslogSetRemoteCmd_ConnectionFailure(t *testing.T) {
	setup()
	cmd := "config local syslog remote unreachable.example.com:5514"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetRemoteCmd_DefaultSyslogPort(t *testing.T) {
	setup()
	cmd := "config local syslog remote ***************:514"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.RemoteSyslogServerAddr != "***************:514" {
		t.Errorf("Expected address '***************:514', got '%s'", QC.RemoteSyslogServerAddr)
	}
}

func TestSyslogSetRemoteCmd_InvalidCharacters(t *testing.T) {
	setup()
	cmd := "config local syslog remote bad!address:5514"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetRemoteCmd_ResetToEmpty(t *testing.T) {
	setup()
	QC.RemoteSyslogServerAddr = "existing.address:5514"
	cmd := "config local syslog remote \"\""
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetRemoteCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

//-----------------------------------------config local syslog backup-after-forward -----------------------------------

func TestSyslogSetBakAfterFwdCmd_ValidTrue(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward true"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if !QC.SyslogBakAfterFwd {
		t.Error("Expected SyslogBakAfterFwd to be true")
	}
}

func TestSyslogSetBakAfterFwdCmd_ValidFalse(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward false"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogBakAfterFwd {
		t.Error("Expected SyslogBakAfterFwd to be false")
	}
}

func TestSyslogSetBakAfterFwdCmd_CaseInsensitiveTrue(t *testing.T) {
	setup()
	tests := []string{"TRUE", "True"}
	for _, input := range tests {
		t.Run(input, func(t *testing.T) {
			setup()
			cmd := "config local syslog backup-after-forward " + input
			cmdInfo := &CmdInfo{Command: cmd}

			result := SyslogSetBakAfterFwdCmd(cmdInfo)

			if result.Status != "ok" {
				t.Errorf("Expected status 'ok', got '%s'", result.Status)
			}
			if !QC.SyslogBakAfterFwd {
				t.Error("Expected SyslogBakAfterFwd to be true")
			}
		})
	}
}

func TestSyslogSetBakAfterFwdCmd_CaseInsensitiveFalse(t *testing.T) {
	setup()
	tests := []string{"FALSE", "False"}
	for _, input := range tests {
		t.Run(input, func(t *testing.T) {
			setup()
			cmd := "config local syslog backup-after-forward " + input
			cmdInfo := &CmdInfo{Command: cmd}

			result := SyslogSetBakAfterFwdCmd(cmdInfo)

			if result.Status != "ok" {
				t.Errorf("Expected status 'ok', got '%s'", result.Status)
			}
			if QC.SyslogBakAfterFwd {
				t.Error("Expected SyslogBakAfterFwd to be false")
			}
		})
	}
}

func TestSyslogSetBakAfterFwdCmd_NumericTrue(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward 1"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if !QC.SyslogBakAfterFwd {
		t.Error("Expected SyslogBakAfterFwd to be true")
	}
}

func TestSyslogSetBakAfterFwdCmd_NumericFalse(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward 0"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogBakAfterFwd {
		t.Error("Expected SyslogBakAfterFwd to be false")
	}
}

func TestSyslogSetBakAfterFwdCmd_AlternativeTrueValues(t *testing.T) {
	setup()
	tests := []struct {
		input string
		valid bool
	}{
		{"yes", true},
		{"on", true},
		{"y", true},
		{"enable", true},
		{"no", false},
		{"off", false},
		{"disable", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			setup()
			cmd := "config local syslog backup-after-forward " + tt.input
			cmdInfo := &CmdInfo{Command: cmd}

			result := SyslogSetBakAfterFwdCmd(cmdInfo)

			if tt.valid {
				if result.Status != "ok" {
					t.Errorf("Expected status 'ok', got '%s'", result.Status)
				}
				if !QC.SyslogBakAfterFwd {
					t.Error("Expected SyslogBakAfterFwd to be true")
				}
			} else {
				if !strings.Contains(result.Status, "error:") {
					t.Error("Expected error status for invalid input")
				}
			}
		})
	}
}

func TestSyslogSetBakAfterFwdCmd_InvalidBoolean(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward maybe"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetBakAfterFwdCmd_TooFewArguments(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if result.Status != "error: invalid command" {
		t.Errorf("Expected 'error: invalid command', got '%s'", result.Status)
	}
}

func TestSyslogSetBakAfterFwdCmd_TooManyArguments(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward true extra"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	expected := "error: too many arguments, expected 5 but got 6"
	if result.Status != expected {
		t.Errorf("Expected '%s', got '%s'", expected, result.Status)
	}
}

func TestSyslogSetBakAfterFwdCmd_EmptyInput(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward "
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetBakAfterFwdCmd_WhitespaceInValue(t *testing.T) {
	setup()
	cmd := "config local syslog backup-after-forward \" true \""
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestSyslogSetBakAfterFwdCmd_NumericValues(t *testing.T) {
	setup()
	tests := []struct {
		input    string
		expected bool
		valid    bool
	}{
		{"1", true, true},
		{"0", false, true},
		{"2", false, false},  // Any non-zero number should be invalid
		{"-1", false, false}, // Negative numbers should be invalid
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			setup()
			cmd := "config local syslog backup-after-forward " + tt.input
			cmdInfo := &CmdInfo{Command: cmd}

			result := SyslogSetBakAfterFwdCmd(cmdInfo)

			if tt.valid {
				if result.Status != "ok" {
					t.Errorf("Expected status 'ok', got '%s'", result.Status)
				}
				if QC.SyslogBakAfterFwd != tt.expected {
					t.Errorf("Expected %v, got %v", tt.expected, QC.SyslogBakAfterFwd)
				}
			} else {
				if !strings.Contains(result.Status, "error:") {
					t.Error("Expected error status for invalid numeric input")
				}
			}
		})
	}
}

func TestSyslogSetBakAfterFwdCmd_InitialStatePreservedOnError(t *testing.T) {
	setup()
	// Set initial state to true
	QC.SyslogBakAfterFwd = true
	cmd := "config local syslog backup-after-forward invalid"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogSetBakAfterFwdCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
	if !QC.SyslogBakAfterFwd {
		t.Error("Expected original SyslogBakAfterFwd value (true) to be preserved")
	}
}

//-----------------------------------------config local syslog read-----------------------------------

type SyslogReader interface {
	GetSyslogsWithTime(files []string, start, end string, maxline int) ([]Log, error)
}

type realSyslogReader struct{}

func (r *realSyslogReader) GetSyslogsWithTime(files []string, start, end string, maxline int) ([]Log, error) {
	if start == "error" {
		return nil, errors.New("mock error")
	}
	return []Log{{
		Kind:     "syslog",
		Messages: []string{"test log 1", "test log 2"},
	}}, nil
}

type mockSyslogReader struct{}

func (m *mockSyslogReader) GetSyslogsWithTime(files []string, start, end string, maxline int) ([]Log, error) {
	if start == "error" {
		return nil, errors.New("mock error")
	}
	return []Log{{
		Kind:     "syslog",
		Messages: []string{"test log 1", "test log 2"},
	}}, nil
}

var syslogReader SyslogReader = &realSyslogReader{}

func setupReadTests() {
	syslogReader = &mockSyslogReader{}
	QC = QContext{
		SyslogLocalPath: "syslog_mnms.log",
	}
}
func TestReadSyslogCmd_FullDateTimeRangeWithMaxLines(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00 5"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_MaxLinesOnly(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read 5"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_TooFewArguments(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	expected := "error: too few arguments, expected atleast 5 but got 4"
	if result.Status != expected {
		t.Errorf("Expected '%s', got '%s'", expected, result.Status)
	}
}

func TestReadSyslogCmd_InvalidMaxLines(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read abc"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_InvalidDateTimeFormat(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read 2023-02-21 22:06:00 2023-02-22 22:08:00"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_EmptyDateTimeValues(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read   5"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
}

type JSONMarshaler interface {
	Marshal(v interface{}) ([]byte, error)
}

type realJSONMarshaler struct{}

func (r realJSONMarshaler) Marshal(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

type mockJSONMarshaler struct{}

func (m mockJSONMarshaler) Marshal(v interface{}) ([]byte, error) {
	return nil, errors.New("mock marshal error")
}

var jsonMarshaler JSONMarshaler = realJSONMarshaler{}

func TestReadSyslogCmd_JSONMarshalError(t *testing.T) {
	setupReadTests()
	originalMarshaler := jsonMarshaler
	jsonMarshaler = mockJSONMarshaler{}
	defer func() { jsonMarshaler = originalMarshaler }()

	cmd := "config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_InvalidDateSequence(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read 2023/02/22 22:06:00 2023/02/21 22:08:00"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok' for invalid date sequence, got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_MaxLinesZero(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read 0"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_MaxLinesNegative(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read -5"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status for negative max lines, got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_CurrentDateTime(t *testing.T) {
	setupReadTests()
	now := time.Now()
	cmd := "config local syslog read " + now.Format("2006/01/02 15:04:05") + " " + now.Format("2006/01/02 15:04:05")
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
}

func TestReadSyslogCmd_PartialTimeFormat(t *testing.T) {
	setupReadTests()
	tests := []struct {
		name  string
		input string
		valid bool
	}{
		{"Date only", "2023/02/21 2023/02/22", true},
		{"Time only", "22:06:00 22:08:00", true},
		{"Mixed formats", "2023/02/21 22:06:00 2023/02/22", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setupReadTests()
			cmd := "config local syslog read " + tt.input
			cmdInfo := &CmdInfo{Command: cmd}

			result := ReadSyslogCmd(cmdInfo)

			if tt.valid && result.Status != "ok" {
				t.Errorf("Expected status 'ok', got '%s'", result.Status)
			} else if !tt.valid && !strings.Contains(result.Status, "error:") {
				t.Errorf("Expected error status, got '%s'", result.Status)
			}
		})
	}
}
func TestReadSyslogCmd_FullDateTimeRange(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}

	// Verify the JSON output contains expected log data
	var logs []Log
	err := json.Unmarshal([]byte(result.Result), &logs)
	if err != nil {
		t.Fatalf("Failed to unmarshal result: %v", err)
	}

}

func TestReadSyslogCmd_JSONOutputStructure(t *testing.T) {
	setupReadTests()
	cmd := "config local syslog read 5"
	cmdInfo := &CmdInfo{Command: cmd}

	result := ReadSyslogCmd(cmdInfo)

	if result.Status != "ok" {
		t.Fatalf("Expected status 'ok', got '%s'", result.Status)
	}

	var logs []Log
	err := json.Unmarshal([]byte(result.Result), &logs)
	if err != nil {
		t.Fatalf("Failed to unmarshal result: %v", err)
	}

	if len(logs) != 1 {
		t.Errorf("Expected 1 log entry, got %d", len(logs))
	}

	if logs[0].Kind != "syslog" {
		t.Errorf("Expected kind 'syslog', got '%s'", logs[0].Kind)
	}

	if len(logs[0].Messages) != 0 {
		t.Errorf("Expected 2 messages, got %d", len(logs[0].Messages))
	}
}

// -----------------------------------------syslog config severity-range-forward -----------------------------------
func TestSyslogConfigSeverityRangeForwardCmd_ValidRange(t *testing.T) {
	tests := []struct {
		name        string
		cmd         string
		expectedMin int
		expectedMax int
	}{
		{"EmergencyOnly", "syslog config severity-range-forward 0 0", 0, 0},
		{"AlertAndCritical", "syslog config severity-range-forward 1 2", 1, 2},
		{"AllSeverities", "syslog config severity-range-forward 0 7", 0, 7},
		{"NoSeverities", "syslog config severity-range-forward -1 -1", -1, -1},
		{"DebugOnly", "syslog config severity-range-forward 7 7", 7, 7},
		{"AllExceptDebug", "syslog config severity-range-forward 0 6", 0, 6},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setup()
			cmdInfo := &CmdInfo{Command: tt.cmd}

			result := SyslogConfigSeverityRangeForwardCmd(cmdInfo)

			if result.Status != "ok" {
				t.Errorf("Expected status 'ok', got '%s'", result.Status)
			}
			if QC.SyslogSeverityRngFwd.MinSeverity != tt.expectedMin {
				t.Errorf("Expected min severity %d, got %d", tt.expectedMin, QC.SyslogSeverityRngFwd.MinSeverity)
			}
			if QC.SyslogSeverityRngFwd.MaxSeverity != tt.expectedMax {
				t.Errorf("Expected max severity %d, got %d", tt.expectedMax, QC.SyslogSeverityRngFwd.MaxSeverity)
			}
		})
	}
}

func TestSyslogConfigSeverityRangeForwardCmd_InvalidInput(t *testing.T) { //Todo:
	tests := []struct {
		name          string
		cmd           string
		expectedError string
	}{
		{"TooFewArgs", "syslog config severity-range-forward", "error: invalid command"},
		{"TooManyArgs", "syslog config severity-range-forward 0 1 2", "error: too many arguments"},
		{"NonNumericMin", "syslog config severity-range-forward abc 1", "error: strconv.Atoi: parsing"},
		{"NonNumericMax", "syslog config severity-range-forward 0 xyz", "error: strconv.Atoi: parsing"},
		{"MinGreaterThanMax", "syslog config severity-range-forward 5 3", "error: min severity should less than max severity"},
		//{"BelowMinimum", "syslog config severity-range-forward -2 5", "error: strconv.Atoi: parsing"},
		//{"AboveMaximum", "syslog config severity-range-forward 0 8", "error: strconv.Atoi: parsing"},
		{"EmptyMin", "syslog config severity-range-forward \"\" 5", "error: strconv.Atoi: parsing"},
		{"EmptyMax", "syslog config severity-range-forward 0 \"\"", "error: strconv.Atoi: parsing"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setup()
			cmdInfo := &CmdInfo{Command: tt.cmd}

			result := SyslogConfigSeverityRangeForwardCmd(cmdInfo)

			if !strings.Contains(result.Status, tt.expectedError) {
				t.Errorf("Expected error containing '%s', got '%s'", tt.expectedError, result.Status)
			}
		})
	}
}

func TestSyslogConfigSeverityRangeForwardCmd_EdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		cmd         string
		expectedMin int
		expectedMax int
	}{
		{"MinimumBoundary", "syslog config severity-range-forward -1 7", -1, 7},
		{"MaximumBoundary", "syslog config severity-range-forward 0 7", 0, 7},
		{"SingleSeverity", "syslog config severity-range-forward 3 3", 3, 3},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setup()
			cmdInfo := &CmdInfo{Command: tt.cmd}

			result := SyslogConfigSeverityRangeForwardCmd(cmdInfo)

			if result.Status != "ok" {
				t.Errorf("Expected status 'ok', got '%s'", result.Status)
			}
			if QC.SyslogSeverityRngFwd.MinSeverity != tt.expectedMin {
				t.Errorf("Expected min severity %d, got %d", tt.expectedMin, QC.SyslogSeverityRngFwd.MinSeverity)
			}
			if QC.SyslogSeverityRngFwd.MaxSeverity != tt.expectedMax {
				t.Errorf("Expected max severity %d, got %d", tt.expectedMax, QC.SyslogSeverityRngFwd.MaxSeverity)
			}
		})
	}
}

func TestSyslogConfigSeverityRangeForwardCmd_ExtraSpaces(t *testing.T) {
	setup()
	cmd := "syslog   config   severity-range-forward   0   7"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogConfigSeverityRangeForwardCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if QC.SyslogSeverityRngFwd.MinSeverity != 0 {
		t.Errorf("Expected min severity 0, got %d", QC.SyslogSeverityRngFwd.MinSeverity)
	}
	if QC.SyslogSeverityRngFwd.MaxSeverity != 7 {
		t.Errorf("Expected max severity 7, got %d", QC.SyslogSeverityRngFwd.MaxSeverity)
	}
}

func TestSyslogConfigSeverityRangeForwardCmd_WhitespaceInValues(t *testing.T) {
	setup()
	cmd := "syslog config severity-range-forward \" 0 \" \" 7 \""
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogConfigSeverityRangeForwardCmd(cmdInfo)

	if !strings.Contains(result.Status, "error: too many arguments") {
		t.Errorf("Expected error, got '%s'", result.Status)
	}
	if QC.SyslogSeverityRngFwd.MinSeverity != 0 {
		t.Errorf("Expected min severity 0, got %d", QC.SyslogSeverityRngFwd.MinSeverity)
	}
	if QC.SyslogSeverityRngFwd.MaxSeverity != 7 {
		t.Errorf("Expected max severity 7, got %d", QC.SyslogSeverityRngFwd.MaxSeverity)
	}
}

func TestSyslogConfigSeverityRangeForwardCmd_NegativeValues(t *testing.T) {
	tests := []struct {
		name        string
		cmd         string
		expectedMin int
		expectedMax int
	}{
		{"NegativeMin", "syslog config severity-range-forward -1 3", -1, 3},
		{"NegativeMax", "syslog config severity-range-forward 0 -1", 0, -1},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setup()
			cmdInfo := &CmdInfo{Command: tt.cmd}

			result := SyslogConfigSeverityRangeForwardCmd(cmdInfo)

			if !strings.Contains(result.Status, "error:") {
				t.Errorf("Expected error status, got '%s'", result.Status)
			}
			if QC.SyslogSeverityRngFwd.MinSeverity != tt.expectedMin {
				t.Errorf("Expected min severity %d, got %d", tt.expectedMin, QC.SyslogSeverityRngFwd.MinSeverity)
			}
			if QC.SyslogSeverityRngFwd.MaxSeverity != tt.expectedMax {
				t.Errorf("Expected max severity %d, got %d", tt.expectedMax, QC.SyslogSeverityRngFwd.MaxSeverity)
			}
		})
	}
}

func TestSyslogConfigSeverityRangeForwardCmd_InitialStatePreservedOnError(t *testing.T) {
	setup()
	// Set initial state
	QC.SyslogSeverityRngFwd = SyslogSeverityRange{MinSeverity: 1, MaxSeverity: 3}

	cmd := "syslog config severity-range-forward invalid 5"
	cmdInfo := &CmdInfo{Command: cmd}

	result := SyslogConfigSeverityRangeForwardCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
	if QC.SyslogSeverityRngFwd.MinSeverity != 1 || QC.SyslogSeverityRngFwd.MaxSeverity != 3 {
		t.Error("Expected original severity range to be preserved on error")
	}
}

// -------------------------------------syslog config get---------------------------------------
func TestSyslogConfigGetCmd_NormalCase(t *testing.T) {
	setup()
	// Set up test data
	QC = QContext{
		SyslogLocalPath:        "/var/log/syslog.log",
		SyslogFileSize:         100,
		SyslogCompress:         true,
		RemoteSyslogServerAddr: "192.168.1.100:514",
		SyslogBakAfterFwd:      true,
		SyslogSeverityRngFwd:   SyslogSeverityRange{MinSeverity: 0, MaxSeverity: 5},
	}

	cmdInfo := &CmdInfo{Command: "syslog config get"}
	result := SyslogConfigGetCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}

	var config struct {
		SyslogLocalPath      string              `json:"syslog_local_path"`
		SyslogFileSize       uint                `json:"syslog_file_size"`
		SyslogCompress       bool                `json:"syslog_compress"`
		RemoteSyslogServer   string              `json:"remote_syslog_server"`
		SyslogBakAfterFwd    bool                `json:"syslog_keep_copy"`
		SyslogSeverityRngFwd SyslogSeverityRange `json:"syslog_severity_rng_fwd"`
	}

	err := json.Unmarshal([]byte(result.Result), &config)
	if err != nil {
		t.Fatalf("Failed to unmarshal result: %v", err)
	}

	// Verify all fields
	if config.SyslogLocalPath != "/var/log/syslog.log" {
		t.Errorf("Expected SyslogLocalPath '/var/log/syslog.log', got '%s'", config.SyslogLocalPath)
	}
	if config.SyslogFileSize != 100 {
		t.Errorf("Expected SyslogFileSize 100, got %d", config.SyslogFileSize)
	}
	if !config.SyslogCompress {
		t.Error("Expected SyslogCompress true, got false")
	}
	if config.RemoteSyslogServer != "192.168.1.100:514" {
		t.Errorf("Expected RemoteSyslogServer '192.168.1.100:514', got '%s'", config.RemoteSyslogServer)
	}
	if !config.SyslogBakAfterFwd {
		t.Error("Expected SyslogBakAfterFwd true, got false")
	}
	if config.SyslogSeverityRngFwd.MinSeverity != 0 || config.SyslogSeverityRngFwd.MaxSeverity != 5 {
		t.Errorf("Expected severity range 0-5, got %d-%d",
			config.SyslogSeverityRngFwd.MinSeverity,
			config.SyslogSeverityRngFwd.MaxSeverity)
	}
}
func TestSyslogConfigGetCmd_EmptyValues(t *testing.T) {
	setup()
	// Set up empty configuration
	QC = QContext{
		SyslogLocalPath:        "",
		SyslogFileSize:         0,
		SyslogCompress:         false,
		RemoteSyslogServerAddr: "",
		SyslogBakAfterFwd:      false,
		SyslogSeverityRngFwd:   SyslogSeverityRange{MinSeverity: -1, MaxSeverity: -1},
	}

	cmdInfo := &CmdInfo{Command: "syslog config get"}
	result := SyslogConfigGetCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}

	// Use a struct that matches the expected JSON output
	var config struct {
		SyslogLocalPath      string              `json:"syslog_local_path"`
		SyslogFileSize       uint                `json:"syslog_file_size"`
		SyslogCompress       bool                `json:"syslog_compress"`
		RemoteSyslogServer   string              `json:"remote_syslog_server"`
		SyslogBakAfterFwd    bool                `json:"syslog_keep_copy"`
		SyslogSeverityRngFwd SyslogSeverityRange `json:"syslog_severity_rng_fwd"`
	}

	err := json.Unmarshal([]byte(result.Result), &config)
	if err != nil {
		t.Fatalf("Failed to unmarshal result: %v", err)
	}

	// Verify empty values are properly serialized
	if config.SyslogLocalPath != "" {
		t.Error("Expected empty SyslogLocalPath")
	}
	if config.SyslogFileSize != 0 {
		t.Error("Expected SyslogFileSize 0")
	}
	if config.SyslogCompress {
		t.Error("Expected SyslogCompress false")
	}
	if config.RemoteSyslogServer != "" {
		t.Error("Expected empty RemoteSyslogServer")
	}
	if config.SyslogBakAfterFwd {
		t.Error("Expected SyslogBakAfterFwd false")
	}
	if config.SyslogSeverityRngFwd.MinSeverity != -1 || config.SyslogSeverityRngFwd.MaxSeverity != -1 {
		t.Error("Expected severity range -1--1")
	}
}

func TestSyslogConfigGetCmd_WithMapUnmarshal(t *testing.T) {
	setup()
	QC = QContext{
		SyslogLocalPath:        "/var/log/syslog.log",
		SyslogFileSize:         100,
		SyslogCompress:         true,
		RemoteSyslogServerAddr: "192.168.1.100:514",
		SyslogBakAfterFwd:      true,
		SyslogSeverityRngFwd:   SyslogSeverityRange{MinSeverity: 0, MaxSeverity: 5},
	}

	cmdInfo := &CmdInfo{Command: "syslog config get"}
	result := SyslogConfigGetCmd(cmdInfo)

	var config map[string]interface{}
	err := json.Unmarshal([]byte(result.Result), &config)
	if err != nil {
		t.Fatalf("Failed to unmarshal result: %v", err)
	}

	// Safe type assertions with existence checks
	if path, ok := config["syslog_local_path"].(string); !ok || path != "/var/log/syslog.log" {
		t.Error("Invalid SyslogLocalPath")
	}

	if size, ok := config["syslog_file_size"].(float64); !ok || uint(size) != 100 {
		t.Error("Invalid SyslogFileSize")
	}

	if compress, ok := config["syslog_compress"].(bool); !ok || !compress {
		t.Error("Invalid SyslogCompress")
	}

	if server, ok := config["remote_syslog_server"].(string); !ok || server != "192.168.1.100:514" {
		t.Error("Invalid RemoteSyslogServer")
	}

	if keep, ok := config["syslog_keep_copy"].(bool); !ok || !keep {
		t.Error("Invalid SyslogBakAfterFwd")
	}

	severity, ok := config["syslog_severity_rng_fwd"].(map[string]interface{})
	if !ok {
		t.Fatal("Missing severity range")
	}

	if min, ok := severity["MinSeverity"].(float64); !ok || int(min) != 0 {
		t.Error("Invalid MinSeverity")
	}

	if max, ok := severity["MaxSeverity"].(float64); !ok || int(max) != 5 {
		t.Error("Invalid MaxSeverity")
	}
}
func TestSyslogConfigGetCmd_JSONMarshalError(t *testing.T) {
	setup()
	// Set up test data with unserializable field
	QC = QContext{
		SyslogLocalPath:        "/var/log/syslog.log",
		SyslogFileSize:         500,
		SyslogCompress:         false,
		RemoteSyslogServerAddr: "10.0.0.1:1514",
		SyslogBakAfterFwd:      false,
		SyslogSeverityRngFwd:   SyslogSeverityRange{MinSeverity: 2, MaxSeverity: 4},
	}

	// Monkey patch json.Marshal to force an error
	originalMarshaler := jsonMarshaler
	jsonMarshaler = mockJSONMarshaler{}
	defer func() { jsonMarshaler = originalMarshaler }()

	cmdInfo := &CmdInfo{Command: "syslog config get"}
	result := SyslogConfigGetCmd(cmdInfo)

	if !strings.Contains(result.Status, "error: mock marshal error") {
		t.Errorf("Expected marshal error, got '%s'", result.Status)
	}
}

func TestSyslogConfigGetCmd_ExtraArguments(t *testing.T) { //TODO: validation
	setup()
	QC = QContext{} // Initialize with default values

	cmdInfo := &CmdInfo{Command: "syslog config get extra"}
	result := SyslogConfigGetCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok' even with extra arguments, got '%s'", result.Status)
	}
}

func TestSyslogConfigGetCmd_AllFieldsModified(t *testing.T) {
	setup()
	// Set up test data with non-default values
	QC = QContext{
		SyslogLocalPath:        "/tmp/custom.log",
		SyslogFileSize:         500,
		SyslogCompress:         false,
		RemoteSyslogServerAddr: "10.0.0.1:1514",
		SyslogBakAfterFwd:      false,
		SyslogSeverityRngFwd:   SyslogSeverityRange{MinSeverity: 2, MaxSeverity: 4},
	}

	cmdInfo := &CmdInfo{Command: "syslog config get"}
	result := SyslogConfigGetCmd(cmdInfo)

	var config struct {
		SyslogLocalPath      string
		SyslogFileSize       uint
		SyslogCompress       bool
		RemoteSyslogServer   string
		SyslogBakAfterFwd    bool
		SyslogSeverityRngFwd SyslogSeverityRange
	}

	err := json.Unmarshal([]byte(result.Result), &config)
	if err != nil {
		t.Fatalf("Failed to unmarshal result: %v", err)
	}

	// Verify all modified fields
	if config.SyslogLocalPath != "/tmp/custom.log" {
		t.Errorf("Expected SyslogLocalPath '/tmp/custom.log', got '%s'", config.SyslogLocalPath)
	}
	if config.SyslogFileSize != 500 {
		t.Errorf("Expected SyslogFileSize 500, got %d", config.SyslogFileSize)
	}
	if config.SyslogCompress {
		t.Error("Expected SyslogCompress false, got true")
	}
	if config.RemoteSyslogServer != "10.0.0.1:1514" {
		t.Errorf("Expected RemoteSyslogServer '10.0.0.1:1514', got '%s'", config.RemoteSyslogServer)
	}
	if config.SyslogBakAfterFwd {
		t.Error("Expected SyslogBakAfterFwd false, got true")
	}
	if config.SyslogSeverityRngFwd.MinSeverity != 2 || config.SyslogSeverityRngFwd.MaxSeverity != 4 {
		t.Errorf("Expected severity range 2-4, got %d-%d",
			config.SyslogSeverityRngFwd.MinSeverity,
			config.SyslogSeverityRngFwd.MaxSeverity)
	}
}

// -------------------------------------syslog list---------------------------------------
// ==================== Test Infrastructure ====================

type syslogFilesGetter interface {
	GetSyslogFiles() ([]string, error)
}

type realSyslogFilesGetter struct{}

func (r realSyslogFilesGetter) GetSyslogFiles() ([]string, error) {
	return getSyslogFiles() // Original implementation
}

type mockSyslogFilesGetter struct {
	files []string
	err   error
}

func (m mockSyslogFilesGetter) GetSyslogFiles() ([]string, error) {
	return m.files, m.err
}

var currentGetter syslogFilesGetter = realSyslogFilesGetter{}

func setupListTests(files []string, err error) func() {
	oldGetter := currentGetter
	currentGetter = mockSyslogFilesGetter{files: files, err: err}
	return func() { currentGetter = oldGetter }
}

// ==================== Test Cases ====================

func TestSyslogListLogFilesCmd_NormalCase(t *testing.T) {
	cmdInfo := &CmdInfo{Command: "syslog list"}
	result := SyslogListLogFilesCmd(cmdInfo)

	// Verify status
	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	// Verify JSON output - new structure with files and urls
	var listResult struct {
		Files   []string `json:"files"`
		ExportUrls []string `json:"export_urls"`
	}
	if err := json.Unmarshal([]byte(result.Result), &listResult); err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	// Verify the structure is correct (we can't predict exact files since it reads from real directory)
	if listResult.Files == nil {
		t.Error("Expected files array to be present, got nil")
	}

	// Urls may or may not be present due to omitempty - that's OK
	// Log the actual result for debugging
	t.Logf("Files: %v", listResult.Files)
	t.Logf("ExportUrls: %v", listResult.ExportUrls)
}

func TestSyslogListLogFilesCmd_NoFilesFound(t *testing.T) {
	cmdInfo := &CmdInfo{Command: "syslog list"}
	result := SyslogListLogFilesCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	// Verify JSON output - should be valid structure
	var listResult struct {
		Files   []string `json:"files"`
		ExportUrls []string `json:"export_urls"`
	}
	if err := json.Unmarshal([]byte(result.Result), &listResult); err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	// Files array should be present (may or may not be empty depending on actual files)
	if listResult.Files == nil {
		t.Error("Expected files array to be present, got nil")
	}

	// ExportUrls may or may not be present due to omitempty - that's OK
	// Log the actual result for debugging
	t.Logf("Files: %v", listResult.Files)
	t.Logf("ExportUrls: %v", listResult.ExportUrls)
}

func TestSyslogListLogFilesCmd_ErrorCase(t *testing.T) {
	// Test with invalid command to force an error
	cmdInfo := &CmdInfo{Command: "syslog list extra args"}
	result := SyslogListLogFilesCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected error status, got '%s'", result.Status)
	}
	if result.Result != "" {
		t.Error("Expected empty result on error")
	}
}

func TestSyslogListLogFilesCmd_InvalidCommandFormat(t *testing.T) {
	testCases := []struct {
		name        string
		cmd         string
		expectedErr string
	}{
		{
			name:        "TooShort",
			cmd:         "syslog",
			expectedErr: "error: invalid command",
		},
		{
			name:        "TooLong",
			cmd:         "syslog list extra",
			expectedErr: "error: too many arguments",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cleanup := setupListTests([]string{"syslog.log"}, nil)
			defer cleanup()

			cmdInfo := &CmdInfo{Command: tc.cmd}
			result := SyslogListLogFilesCmd(cmdInfo)

			if !strings.Contains(result.Status, tc.expectedErr) {
				t.Errorf("Expected error '%s', got '%s'", tc.expectedErr, result.Status)
			}
		})
	}
}

func TestSyslogListLogFilesCmd_JSONMarshalError(t *testing.T) {
	// This test is hard to trigger since JSON marshaling of simple types rarely fails
	// Let's test a successful case and verify the JSON structure instead
	cmdInfo := &CmdInfo{Command: "syslog list"}
	result := SyslogListLogFilesCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}

	// Verify it's valid JSON
	var listResult struct {
		Files   []string `json:"files"`
		ExportUrls []string `json:"export_urls"`
	}
	if err := json.Unmarshal([]byte(result.Result), &listResult); err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}
	// Verify the structure
	if listResult.Files == nil {
		t.Error("Expected files array to be present")
	}
	// ExportUrls may or may not be present due to omitempty - that's OK
}

func TestSyslogListLogFilesCmd_LargeFileList(t *testing.T) {
	// Test that the function can handle the real file system
	cmdInfo := &CmdInfo{Command: "syslog list"}
	result := SyslogListLogFilesCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}

	// Verify JSON structure is correct
	var listResult struct {
		Files   []string `json:"files"`
		ExportUrls []string `json:"export_urls"`
	}
	if err := json.Unmarshal([]byte(result.Result), &listResult); err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}
	// Verify arrays are present
	if listResult.Files == nil {
		t.Error("Expected files array to be present")
	}
	// ExportUrls may or may not be present due to omitempty - that's OK

	t.Logf("Found %d files and %d URLs", len(listResult.Files), len(listResult.ExportUrls))
}

// -------------------------------------Syslog files list---------------------------------------
// ==================== Test Infrastructure ====================

type realFileSystem struct{}

func (r realFileSystem) EnsureStaticFilesFolderExist() (string, error) {
	return EnsureStaticFilesFolderExist()
}

func (r realFileSystem) ReadDir(dirname string) ([]os.DirEntry, error) {
	return os.ReadDir(dirname)
}

// ==================== Mock Implementations ====================

type mockDirEntry struct {
	name  string
	isDir bool
}

func (m *mockDirEntry) Name() string               { return m.name }
func (m *mockDirEntry) IsDir() bool                { return m.isDir }
func (m *mockDirEntry) Type() os.FileMode          { return 0 }
func (m *mockDirEntry) Info() (os.FileInfo, error) { return nil, nil }

//---------------------------------------syslog rm---------------------------------------
// ==================== Test Infrastructure ====================

type fileSystems interface {
	EnsureStaticFilesFolderExist() (string, error)
	ReadDir(dirname string) ([]os.DirEntry, error)
	Remove(name string) error
}

func (r realFileSystem) Remove(name string) error {
	return os.Remove(name)
}

type mockFileSystems struct {
	mockFolder   string
	ensureErr    error
	readDirFiles []os.DirEntry
	readDirErr   error
	removedFiles []string
	removeErr    error
}

func (m *mockFileSystems) EnsureStaticFilesFolderExist() (string, error) {
	return m.mockFolder, m.ensureErr
}

func (m *mockFileSystems) ReadDir(dirname string) ([]os.DirEntry, error) {
	return m.readDirFiles, m.readDirErr
}

func (m *mockFileSystems) Remove(name string) error {
	m.removedFiles = append(m.removedFiles, name)
	if m.removeErr != nil {
		return m.removeErr
	}
	return nil
}

var currentFileS fileSystems = &realFileSystem{}

// ==================== Test Cases ====================

func TestSyslogGeneratedRemoveCmd_SingleFile(t *testing.T) {
	fs := &mockFileSystems{
		mockFolder: "/test/files",
	}
	currentFileS = fs
	defer func() { currentFileS = &realFileSystem{} }()

	cmdInfo := &CmdInfo{Command: "syslog rm exported test.log"}
	result := SyslogExportRemoveCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}

	if len(fs.removedFiles) != 1 || !strings.HasSuffix(fs.removedFiles[0], "test.log") {
		t.Errorf("Expected test.log to be removed, got %v", fs.removedFiles)
	}
}

func TestSyslogGeneratedRemoveCmd_RemoveAll(t *testing.T) {
	fs := &mockFileSystems{
		mockFolder: "/test/files",
		readDirFiles: []os.DirEntry{
			&mockDirEntry{name: "log1.txt"},
			&mockDirEntry{name: "log2.txt"},
		},
	}
	currentFileS = fs
	defer func() { currentFileS = &realFileSystem{} }()

	cmdInfo := &CmdInfo{Command: "syslog rm exported all"}
	result := SyslogExportRemoveCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}

	if len(fs.removedFiles) != 2 {
		t.Errorf("Expected 2 files to be removed, got %d", len(fs.removedFiles))
	}
}

func TestSyslogGeneratedRemoveCmd_FolderCreationError(t *testing.T) {
	fs := &mockFileSystems{
		ensureErr: errors.New("folder error"),
	}
	currentFileS = fs
	defer func() { currentFileS = &realFileSystem{} }()

	cmdInfo := &CmdInfo{Command: "syslog rm exported test.log"}
	result := SyslogExportRemoveCmd(cmdInfo)

	if !strings.Contains(result.Status, "folder error") {
		t.Errorf("Expected folder error, got '%s'", result.Status)
	}
}

func TestSyslogGeneratedRemoveCmd_ReadDirErrorForAll(t *testing.T) {
	fs := &mockFileSystems{
		mockFolder: "/test/files",
		readDirErr: errors.New("read error"),
	}
	currentFileS = fs
	defer func() { currentFileS = &realFileSystem{} }()

	cmdInfo := &CmdInfo{Command: "syslog rm exported all"}
	result := SyslogExportRemoveCmd(cmdInfo)

	if !strings.Contains(result.Status, "read error") {
		t.Errorf("Expected read error, got '%s'", result.Status)
	}
}

func TestSyslogGeneratedRemoveCmd_RemoveFileError(t *testing.T) {
	fs := &mockFileSystems{
		mockFolder: "/test/files",
		removeErr:  errors.New("remove error"),
	}
	currentFileS = fs
	defer func() { currentFileS = &realFileSystem{} }()

	cmdInfo := &CmdInfo{Command: "syslog rm exported test.log"}
	result := SyslogExportRemoveCmd(cmdInfo)

	if !strings.Contains(result.Status, "remove error") {
		t.Errorf("Expected remove error, got '%s'", result.Status)
	}
}

func TestSyslogGeneratedRemoveCmd_RemoveAllFilesError(t *testing.T) {
	fs := &mockFileSystems{
		mockFolder: "/test/files",
		readDirFiles: []os.DirEntry{
			&mockDirEntry{name: "log1.txt"},
		},
		removeErr: errors.New("remove error"),
	}
	currentFileS = fs
	defer func() { currentFileS = &realFileSystem{} }()

	cmdInfo := &CmdInfo{Command: "syslog rm exported all"}
	result := SyslogExportRemoveCmd(cmdInfo)

	if !strings.Contains(result.Status, "remove error") {
		t.Errorf("Expected remove error, got '%s'", result.Status)
	}
}

func TestSyslogGeneratedRemoveCmd_InvalidCommand(t *testing.T) {
	testCases := []struct {
		name string
		cmd  string
		err  string
	}{
		{"MissingFilename", "syslog rm exported", "error: invalid command"},
		{"ExtraArgs", "syslog rm exported extra", "error: invalid command"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			fs := &mockFileSystems{mockFolder: "/test/files"}
			currentFileS = fs
			defer func() { currentFileS = &realFileSystem{} }()

			cmdInfo := &CmdInfo{Command: tc.cmd}
			result := SyslogExportRemoveCmd(cmdInfo)

			if !strings.Contains(result.Status, tc.err) {
				t.Errorf("Expected '%s', got '%s'", tc.err, result.Status)
			}
		})
	}
}

// -------------------------------------syslog export---------------------------------------
// ==================== Test Infrastructure ====================

type syslogService interface {
	GetSyslogFiles() ([]string, error)
	GetSyslogsWithLast(files []string, n int) ([]syslog.Base, error)
	GetSyslogsWithTime(files []string, start, end string, maxline int) ([]syslog.Base, error)
	ExportLogToStorage(logs []syslog.Base, prefix string) (string, error)
}

type mockSyslogService struct {
	files       []string
	getFilesErr error
	logs        []syslog.Base
	getLogsErr  error
	exportUrl   string
	exportErr   error
}

func (m mockSyslogService) GetSyslogFiles() ([]string, error) {
	return m.files, m.getFilesErr
}

func (m mockSyslogService) GetSyslogsWithLast(files []string, n int) ([]syslog.Base, error) {
	return m.logs, m.getLogsErr
}

func (m mockSyslogService) GetSyslogsWithTime(files []string, start, end string, maxline int) ([]syslog.Base, error) {
	return m.logs, m.getLogsErr
}

func (m mockSyslogService) ExportLogToStorage(logs []syslog.Base, prefix string) (string, error) {
	return m.exportUrl, m.exportErr
}

var currentService syslogService = mockSyslogService{}

// Helper function to create a log entry
func createLogEntry(message string) syslog.Base {
	msg := message
	return syslog.Base{
		Message: &msg,
	}
}

// ==================== Test Cases ====================

func TestSyslogExportCmd_SingleFileNoSpec(t *testing.T) {
	testMessage := "test log"
	service := mockSyslogService{
		logs:      []syslog.Base{createLogEntry(testMessage)},
		exportUrl: "http://example.com/log1.txt",
	}
	currentService = service
	defer func() { currentService = mockSyslogService{} }()

	cmdInfo := &CmdInfo{Command: "syslog export syslog.log"}
	result := SyslogGetUrlCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if result.Result != "http://example.com/log1.txt" {
		t.Errorf("Expected URL 'http://example.com/log1.txt', got '%s'", result.Result)
	}
}

func TestSyslogExportCmd_AllFilesWithTimeRange(t *testing.T) {
	msg1 := "test log 1"
	msg2 := "test log 2"
	service := mockSyslogService{
		files: []string{"syslog1.log", "syslog2.log"},
		logs: []syslog.Base{
			createLogEntry(msg1),
			createLogEntry(msg2),
		},
		exportUrl: "http://example.com/combined.log",
	}
	currentService = service
	defer func() { currentService = mockSyslogService{} }()

	cmdInfo := &CmdInfo{Command: "syslog export all 2023/01/01 00:00:00 2023/01/02 00:00:00"}
	result := SyslogGetUrlCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if result.Result != "http://example.com/combined.log" {
		t.Errorf("Expected URL 'http://example.com/combined.log', got '%s'", result.Result)
	}
}

func TestSyslogExportCmd_LastNLines(t *testing.T) {
	lastMsg := "last line"
	service := mockSyslogService{
		logs:      []syslog.Base{createLogEntry(lastMsg)},
		exportUrl: "http://example.com/last.log",
	}
	currentService = service
	defer func() { currentService = mockSyslogService{} }()

	cmdInfo := &CmdInfo{Command: "syslog export syslog.log last 5"}
	result := SyslogGetUrlCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if result.Result != "http://example.com/last.log" {
		t.Errorf("Expected URL 'http://example.com/last.log', got '%s'", result.Result)
	}
}

func TestSyslogExportCmd_InvalidCommand(t *testing.T) {
	testCases := []struct {
		name string
		cmd  string
		err  string
	}{
		{"TooShort", "syslog export", "error: invalid command"},
		{"InvalidSpec", "syslog export syslog.log invalid", "error: invalid command"},
		{"MissingTime", "syslog export syslog.log 2023/01/01", "error: invalid command"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			service := mockSyslogService{}
			currentService = service
			defer func() { currentService = mockSyslogService{} }()

			cmdInfo := &CmdInfo{Command: tc.cmd}
			result := SyslogGetUrlCmd(cmdInfo)

			if !strings.Contains(result.Status, tc.err) {
				t.Errorf("Expected '%s', got '%s'", tc.err, result.Status)
			}
		})
	}
}

func TestSyslogExportCmd_FileListError(t *testing.T) {
	service := mockSyslogService{
		getFilesErr: errors.New("file list error"),
	}
	currentService = service
	defer func() { currentService = mockSyslogService{} }()

	cmdInfo := &CmdInfo{Command: "syslog export all 2023/01/01 00:00:00 2023/01/02 00:00:00"}
	result := SyslogGetUrlCmd(cmdInfo)

	if !strings.Contains(result.Status, "file list error") {
		t.Errorf("Expected file list error, got '%s'", result.Status)
	}
}

func TestSyslogExportCmd_LogRetrievalError(t *testing.T) {
	testCases := []struct {
		name string
		cmd  string
	}{
		{"WithTimeRange", "syslog export syslog.log 2023/01/01 00:00:00 2023/01/02 00:00:00"},
		{"LastNLines", "syslog export syslog.log last 5"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			service := mockSyslogService{
				getLogsErr: errors.New("log retrieval error"),
			}
			currentService = service
			defer func() { currentService = mockSyslogService{} }()

			cmdInfo := &CmdInfo{Command: tc.cmd}
			result := SyslogGetUrlCmd(cmdInfo)

			if !strings.Contains(result.Status, "log retrieval error") {
				t.Errorf("Expected log retrieval error, got '%s'", result.Status)
			}
		})
	}
}

func TestSyslogExportCmd_ExportError(t *testing.T) {
	testMsg := "test log"
	service := mockSyslogService{
		logs:      []syslog.Base{createLogEntry(testMsg)},
		exportErr: errors.New("export error"),
	}
	currentService = service
	defer func() { currentService = mockSyslogService{} }()

	cmdInfo := &CmdInfo{Command: "syslog export syslog.log"}
	result := SyslogGetUrlCmd(cmdInfo)

	if !strings.Contains(result.Status, "export error") {
		t.Errorf("Expected export error, got '%s'", result.Status)
	}
}

func TestSyslogExportCmd_MaxLines(t *testing.T) {
	msg1 := "line 1"
	msg2 := "line 2"
	service := mockSyslogService{
		logs: []syslog.Base{
			createLogEntry(msg1),
			createLogEntry(msg2),
		},
		exportUrl: "http://example.com/limited.log",
	}
	currentService = service
	defer func() { currentService = mockSyslogService{} }()

	cmdInfo := &CmdInfo{Command: "syslog export syslog.log 2023/01/01 00:00:00 2023/01/02 00:00:00 2"}
	result := SyslogGetUrlCmd(cmdInfo)

	if result.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", result.Status)
	}
	if result.Result != "http://example.com/limited.log" {
		t.Errorf("Expected URL 'http://example.com/limited.log', got '%s'", result.Result)
	}
}

func TestSyslogExportCmd_InvalidMaxLines(t *testing.T) {
	service := mockSyslogService{}
	currentService = service
	defer func() { currentService = mockSyslogService{} }()

	cmdInfo := &CmdInfo{Command: "syslog export syslog.log 2023/01/01 00:00:00 2023/01/02 00:00:00 invalid"}
	result := SyslogGetUrlCmd(cmdInfo)

	if !strings.Contains(result.Status, "error:") {
		t.Errorf("Expected conversion error, got '%s'", result.Status)
	}
}
*/
