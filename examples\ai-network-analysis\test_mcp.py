#!/usr/bin/env python3
"""
Simple test script to verify MCP server connectivity
"""
import asyncio
import json
import sys
from fastmcp import Client as mcp_client

async def test_mcp_connection():
    """Test basic MCP connection and tool calling"""
    print("Testing MCP connection...")
    
    # Configure servers
    servers = {
        "nimble-api-wrapper": {
            "command": "python",
            "args": ["nimbl_mcp.py"]
        }
    }
    
    try:
        # Create client
        print("Creating MCP client...")
        client = mcp_client(servers)
        
        # Connect
        print("Connecting to MCP server...")
        await client._connect()
        print("✓ Connected successfully")
        
        # Check connection status
        print(f"Connection status: {client.is_connected()}")
        
        # List tools
        print("Listing available tools...")
        tools = await client.list_tools()
        print(f"✓ Found {len(tools)} tools:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        if tools:
            # Test get_commands tool specifically
            commands_tool = next((t for t in tools if t.name == "get_commands"), None)
            if commands_tool:
                print(f"\nTesting tool: get_commands")
                
                try:
                    # Call with minimal timeout for testing
                    result = await asyncio.wait_for(
                        client.call_tool("get_commands", arguments={}),
                        timeout=30.0
                    )
                    print(f"✓ Commands tool call successful")
                    
                    # Parse and show sample command data
                    if result and 'commands' in result:
                        commands = result['commands']
                        metadata = result.get('metadata', {})
                        
                        print(f"  Total commands: {metadata.get('total_commands', 'N/A')}")
                        print(f"  Server time: {metadata.get('server_time_human', 'N/A')}")
                        print(f"  Analysis note: {metadata.get('analysis_note', 'N/A')}")
                        
                        print(f"\n  Sample commands with timestamp analysis:")
                        for i, (cmd_key, cmd_data) in enumerate(commands.items()):
                            if i >= 2:  # Show only first 2 for brevity
                                break
                                
                            print(f"    Command: {cmd_data.get('command', 'N/A')}")
                            print(f"      Created: {cmd_data.get('created_human', 'N/A')}")
                            print(f"      Status: {cmd_data.get('status', 'N/A')}")
                            print(f"      Time Status: {cmd_data.get('time_status', 'N/A')}")
                            print(f"      Urgency: {cmd_data.get('urgency_level', 'N/A')}")
                            print(f"      Execution Context: {cmd_data.get('execution_context', 'N/A')}")
                    
                except asyncio.TimeoutError:
                    print("✗ Commands tool call timed out after 30 seconds")
                except Exception as e:
                    print(f"✗ Commands tool call failed: {e}")
            
            # Also test the first tool for general verification
            tool = tools[0]
            print(f"\nTesting tool: {tool.name}")
            
            try:
                # Call with minimal timeout for testing
                result = await asyncio.wait_for(
                    client.call_tool(tool.name, arguments={}),
                    timeout=30.0
                )
                print(f"✓ Tool call successful: [Output truncated for brevity]")
                
            except asyncio.TimeoutError:
                print("✗ Tool call timed out after 30 seconds")
            except Exception as e:
                print(f"✗ Tool call failed: {e}")
        
        # Close connection
        print("\nClosing connection...")
        await client.close()
        print("✓ Connection closed")
        
    except Exception as e:
        print(f"✗ MCP test failed: {e}")
        return False
    
    return True

def main():
    """Run the test"""
    try:
        success = asyncio.run(test_mcp_connection())
        if success:
            print("\n✓ MCP test completed successfully")
            sys.exit(0)
        else:
            print("\n✗ MCP test failed")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n✗ Test interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n✗ Test error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
