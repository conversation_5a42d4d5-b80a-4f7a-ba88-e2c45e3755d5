#!/usr/bin/env python3
"""
Test script for LLM Network Analyzer timeout fixes
Tests the API timeout configuration and data size handling
"""
import asyncio
import os
import json
from llm_network_analyzer import NetworkAnalysisConfig, LLMNetworkAnalyzer

async def test_timeout_configuration():
    """Test that the timeout configuration is working"""
    print("[TEST] Testing OpenAI API timeout configuration...")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("[ERROR] OPENAI_API_KEY not set")
        return False
    
    # Create config with short timeout for testing
    config = NetworkAnalysisConfig(
        openai_api_key=api_key,
        model="gpt-4o",
        api_timeout=30,  # Short timeout for testing
        max_raw_data_size=10000  # Smaller data size
    )
    
    analyzer = LLMNetworkAnalyzer(config)
    
    # Test with small mock data
    mock_data = {
        "timestamp": "2025-06-20T10:20:10Z",
        "device_data": {
            "devices": {
                "00-60-E9-2E-4B-5B": {
                    "mac": "00-60-E9-2E-4B-5B",
                    "modelname": "Test-Device",
                    "ipaddress": "*************",
                    "hostname": "test-device",
                    "last_seen_human": "2 minutes ago",
                    "device_status": "active"
                }
            }
        },
        "command_data": {
            "commands": {
                "test_command": {
                    "command": "test command",
                    "status": "success",
                    "timestamp": "2025-06-20T10:19:00Z"
                }
            }
        }
    }
    
    try:
        print("[TEST] Testing LLM analysis with timeout settings...")
        result = await analyzer.analyze_with_llm(mock_data)
        
        if "error" in result:
            print(f"[ERROR] Analysis failed: {result['error']}")
            return False
        else:
            print(f"[SUCCESS] Analysis completed: {result.get('summary', 'No summary')}")
            return True
            
    except Exception as e:
        print(f"[ERROR] Test failed with exception: {e}")
        return False

async def test_data_truncation():
    """Test data truncation for large payloads"""
    print("[TEST] Testing data truncation...")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("[ERROR] OPENAI_API_KEY not set")
        return False
    
    config = NetworkAnalysisConfig(
        openai_api_key=api_key,
        max_raw_data_size=5000  # Very small to force truncation
    )
    
    analyzer = LLMNetworkAnalyzer(config)
    
    # Create large mock data that will trigger truncation
    large_device_data = {}
    for i in range(20):  # Create 20 devices
        large_device_data[f"00-60-E9-2E-4B-{i:02d}"] = {
            "mac": f"00-60-E9-2E-4B-{i:02d}",
            "modelname": f"Large-Device-{i}",
            "ipaddress": f"192.168.1.{100+i}",
            "hostname": f"large-device-{i}",
            "last_seen_human": f"{i+1} minutes ago",
            "device_status": "active",
            "detailed_info": "x" * 500  # Add padding to make it large
        }
    
    mock_data = {
        "timestamp": "2025-06-20T10:20:10Z",
        "device_data": {"devices": large_device_data},
        "command_data": {"commands": {}}
    }
    
    original_size = len(json.dumps(mock_data))
    print(f"[TEST] Original data size: {original_size} characters")
    
    try:
        result = await analyzer.analyze_with_llm(mock_data)
        
        if "truncation_note" in result or "error" not in result:
            print("[SUCCESS] Data truncation test passed")
            return True
        else:
            print(f"[WARNING] No truncation detected: {result}")
            return True  # Not necessarily a failure
            
    except Exception as e:
        print(f"[ERROR] Truncation test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("=== LLM Network Analyzer Timeout Fix Tests ===\n")
    
    tests = [
        ("Timeout Configuration", test_timeout_configuration),
        ("Data Truncation", test_data_truncation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = await test_func()
            results.append((test_name, result))
            print(f"[RESULT] {test_name}: {'PASS' if result else 'FAIL'}")
        except Exception as e:
            print(f"[ERROR] {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print(f"\n=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("[SUCCESS] All timeout fix tests passed!")
        return True
    else:
        print("[WARNING] Some tests failed - check configuration and API key")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
