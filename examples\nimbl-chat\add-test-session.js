// Script to add a test session with Mermaid graph to IndexedDB
// Run this in the browser console on localhost:5174

import { setItem, getItem } from "./src/services/indexedDbService.js";

const testSession = {
  id: "test-mermaid-session",
  name: "Mermaid Graph Test",
  provider: "test",
  model: "test-model",
  messages: [
    {
      role: "user",
      content: "Can you show me a network topology diagram?",
    },
    {
      role: "assistant",
      content: `Here's your network topology diagram:

\`\`\`
graph LR
N_00_60_E9_1A_3C_3E["EHG7508-4SFP<br/>IP: *********"] -->|"Port5 to Port 7"| N_00_60_E9_20_C3_03["EHG7512-8PoE-410GSFP<br/>IP: *********"]
N_00_60_E9_20_C3_03 -->|"Port7 to Port 5"| N_00_60_E9_1A_3C_3E
N_00_60_E9_20_C3_03 -->|"Port6 to Port 1"| N_00_60_E9_26_2D_C6["EHG2408<br/>IP: **********"]
N_00_60_E9_26_2D_C6 -->|"Port1 to Port 6"| N_00_60_E9_20_C3_03
\`\`\`

This diagram shows the network connections between your three devices:
- EHG7508-4SFP (*********)
- EHG7512-8PoE-410GSFP (*********)
- EHG2408 (**********)

The connections show the port-to-port relationships between the devices.`,
    },
    {
      role: "user",
      content: "Can you also show a simple flowchart?",
    },
    {
      role: "assistant",
      content: `Here's a simple flowchart example:

\`\`\`
graph TD
A[Start] --> B{Decision Point}
B -->|Yes| C[Process A]
B -->|No| D[Process B]
C --> E[End]
D --> E
\`\`\`

This shows a basic decision flow with two possible paths.`,
    },
    {
      role: "user",
      content: "Can you show me some JSON data?",
    },
    {
      role: "assistant",
      content: "Here's some sample JSON data with proper formatting:",
      tool_calls: [
        {
          id: "call_123",
          type: "function",
          function: {
            name: "get_network_data",
            arguments: {
              device_type: "switch",
              location: "datacenter-1",
              include_ports: true,
              format: "detailed",
            },
          },
        },
      ],
    },
    {
      role: "tool",
      name: "get_network_data",
      content:
        '{"devices":[{"id":"sw-001","name":"Core Switch 1","type":"switch","location":"datacenter-1","ip_address":"********","ports":[{"port_id":"1","status":"up","speed":"1Gbps","connected_device":"srv-001"},{"port_id":"2","status":"up","speed":"1Gbps","connected_device":"srv-002"},{"port_id":"3","status":"down","speed":"1Gbps","connected_device":null}],"uptime":"45 days","firmware_version":"v2.1.3"},{"id":"sw-002","name":"Access Switch 1","type":"switch","location":"datacenter-1","ip_address":"********","ports":[{"port_id":"1","status":"up","speed":"100Mbps","connected_device":"pc-001"},{"port_id":"2","status":"up","speed":"100Mbps","connected_device":"pc-002"}],"uptime":"30 days","firmware_version":"v1.8.2"}],"total_devices":2,"query_timestamp":"2024-01-15T10:30:00Z"}',
    },
    {
      role: "assistant",
      content:
        "As you can see, the JSON data is now properly formatted with indentation, making it much easier to read. The tool call arguments are also formatted nicely, and the sidebar stays fixed when you scroll through long content like this JSON response.",
    },
  ],
};

// Immediately-invoked function expression to handle async code
(async () => {
  // Get existing sessions or create empty array
  const existingSessions = JSON.parse((await getItem("llm_sessions")) || "[]");

  // Remove any existing test session
  const filteredSessions = existingSessions.filter(
    (s) => s.id !== "test-mermaid-session"
  );

  // Add the test session
  filteredSessions.unshift(testSession);

  // Save back to IndexedDB
  await setItem("llm_sessions", JSON.stringify(filteredSessions));

  console.log("Test session added! Refresh the page to see it.");
  console.log(
    "After the page loads, try sending a new message to test if graphs persist."
  );
})();
