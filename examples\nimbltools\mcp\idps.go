package mcp

import (
	"context"
	"errors"
	"fmt"
	"mnms"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

func registerIdps(s *server.MCPServer, es events) {
	addidpsTool(s, es)
}

func addidpsTool(s *server.MCPServer, es events) {
	add := mcp.NewTool("idps_add_rule",
		mcp.WithDescription("To Add rules from a text by user input."),
		mcp.WithString("client",
			mcp.Required(),
			mcp.Description("service name or client name"),
		),
		mcp.WithString("action",
			mcp.Required(),
			mcp.Description("action to take on the packet"),
			mcp.Enum("alert", "pass", "drop"),
		),
		mcp.WithString("sourceip",
			mcp.Description("source ip address"),
		),
		mcp.WithString("sourceport",
			mcp.Description("source port"),
		),
		mcp.WithString("protocol",
			mcp.Required(),
			mcp.Description("network protocol"),
			mcp.Enum(
				"tcp", "udp", "icmp",
				"http", "http2", "ftp",
				"tls", "ssl", "smb", "dns",
				"tftp", "ssh", "smtp",
				"imap", "modbus", "sip",
				"dhcp", "snmp", "enip", "nfs", "ntp",
			),
		),
		mcp.WithString("destinationip",
			mcp.Description("destination ip address"),
		),
		mcp.WithString("destinationport",
			mcp.Description("destination port"),
		),
		mcp.WithString("msg",
			mcp.Required(),
			mcp.Description("the description of the rule"),
		),
		mcp.WithString("sid",
			mcp.Required(),
			mcp.Description("the sid of the rule"),
		),
		mcp.WithString("category",
			mcp.Description("the category name of the rule"),
		),
	)
	s.AddTool(add, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		var client, act, srcip, srcport, proto, dstip, dstport, msg, sid, category string
		if v, ok := request.GetArguments()["client"]; ok {
			client = v.(string)
		} else {
			return nil, errors.New("please describe client")
		}
		if v, ok := request.GetArguments()["action"]; ok {
			act = v.(string)
		} else {
			return nil, errors.New("please describe action")
		}
		if v, ok := request.GetArguments()["sourceip"]; ok {
			srcip = v.(string)
		} else {
			srcip = "any"
		}
		if v, ok := request.GetArguments()["sourceport"]; ok {
			srcport = v.(string)
		} else {
			srcport = "any"
		}
		if v, ok := request.GetArguments()["destinationip"]; ok {
			dstip = v.(string)
		} else {
			dstip = "any"
		}
		if v, ok := request.GetArguments()["destinationport"]; ok {
			dstport = v.(string)
		} else {
			dstport = "any"
		}
		if v, ok := request.GetArguments()["protocol"]; ok {
			proto = v.(string)
		} else {
			return nil, errors.New("please describe protocol")
		}
		if v, ok := request.GetArguments()["msg"]; ok {
			msg = v.(string)
		} else {
			return nil, errors.New("please describe msg")
		}
		if v, ok := request.GetArguments()["sid"]; ok {
			sid = v.(string)
		} else {
			return nil, errors.New("please describe sid")
		}
		if v, ok := request.GetArguments()["category"]; ok {
			category = v.(string)
		} else {
			category = "AI"
		}
		s := fmt.Sprintf(`idps rules add %v %v %v %v %v -> %v %v (msg:"%v";sid:%v;)`, category, act, proto, srcip, srcport, dstip, dstport, msg, sid)
		cmd := mnms.CmdInfo{Command: s, Client: client}
		_, status, err := es.Post(cmd, true)
		if err != nil {
			return nil, fmt.Errorf("time out:%v", err)
		}
		return mcp.NewToolResultText(string(status)), nil
	})
}
