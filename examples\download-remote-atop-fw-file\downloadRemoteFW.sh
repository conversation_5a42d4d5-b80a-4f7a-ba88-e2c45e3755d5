#!/bin/bash

# -- Default values --
DEST_DIR=""

# -- Function to display usage information --
usage() {
    echo "Usage: $0 -d [Dir] [URL]"
    echo "  -h       : Display this help messages."
    echo "  -d [Dir] : Required. Directory to save the downloaded file."
    echo "  [URL]    : Required. The URL of the ZIP file."
    exit 1
}

# -- Parse command-line options --
while getopts "hd:" opt; do
  case ${opt} in
    d )
      DEST_DIR=$OPTARG
      ;;
    h )
      usage
      exit 0
      ;;  
    \? )
      echo "Error: Invalid option: -$OPTARG" 1>&2
      usage
      ;;
    : )
      echo "Error: Option -$OPTARG requires an argument." 1>&2
      usage
      ;;
  esac
done
shift $((OPTIND -1))

# Check if the -d option was provided
if [ -z "$DEST_DIR" ]; then
    echo "Error: The -d option for destination directory is required."
    usage
fi

# -- Check for required URL argument --
if [ -z "$1" ]; then
    echo "Error: URL is a required argument."
    usage
fi

DEST_DIR=$(sed 's,[/\\]$,,' <<< "$DEST_DIR")

# ==============================================================================



# -- Define main variables --
URL="$1"
FILENAME=$(basename "$URL")
DEST_FILE="$DEST_DIR/$FILENAME"

# -- Check for required tools --
for cmd in curl unzip; do
    if ! command -v $cmd &> /dev/null; then
        echo "Error: Required command '$cmd' is not installed. Please install it to continue."
        exit 1
    fi
done

# -- Prepare destination directory --
if [ ! -d "$DEST_DIR" ]; then
    echo "Destination directory '$DEST_DIR' does not exist. Creating it..."
    mkdir -p "$DEST_DIR"
    if [ $? -ne 0 ]; then
        echo "Error: Could not create directory '$DEST_DIR'. Please check permissions."
        exit 1
    fi
fi

# --- Step 1: Get expected file size from server header ---
echo "Fetching file metadata from server..."
# Use curl -sI to get headers silently. Grep is case-insensitive.
EXPECTED_SIZE=$(curl -sI "$URL" | grep -i 'Content-Length' | awk '{print $2}' | tr -d '\r')

if [ -z "$EXPECTED_SIZE" ]; then
    echo "Warning: Server did not provide 'Content-Length' header. Skipping file size check."
else
    echo "Expected file size: $EXPECTED_SIZE bytes"
fi
echo "----------------------------------------"

# --- Step 2: Download the file ---
echo "Downloading file from:"
echo "$URL"
echo "Saving to: $DEST_FILE"

# Use curl to download the file. -L follows redirects.
curl -L -o "$DEST_FILE" "$URL"

# Check if curl executed successfully
if [ $? -ne 0 ]; then
    echo "Error: File download failed. Check the URL or your network connection."
    # Clean up incomplete file if it exists
    [ -f "$DEST_FILE" ] && rm "$DEST_FILE"
    exit 1
fi

echo "File download complete."
echo "----------------------------------------"

# --- Step 3: Verify Integrity (Part 1: File Size) ---
if [ -n "$EXPECTED_SIZE" ]; then
    echo "Verifying file size..."

    # Get actual file size. 'stat' command differs on Linux and macOS.
    if [[ "$(uname)" == "Darwin" ]]; then # macOS
        ACTUAL_SIZE=$(stat -f%z "$DEST_FILE")
    else # Linux
        ACTUAL_SIZE=$(stat -c%s "$DEST_FILE")
    fi

    echo "Expected size: $EXPECTED_SIZE bytes"
    echo "Actual size:   $ACTUAL_SIZE bytes"

    if [ "$ACTUAL_SIZE" -eq "$EXPECTED_SIZE" ]; then
        echo "File size verification successful."
    else
        echo "File size verification FAILED! The file may be incomplete or corrupted."
        echo "Deleting downloaded file: $DEST_FILE"
        rm "$DEST_FILE"
        exit 1
    fi
    echo "----------------------------------------"
fi


# --- Step 4: Verify Integrity (Part 2: ZIP Archive Structure) ---
echo "Testing ZIP archive integrity..."

# Use 'unzip -t' (test) in quiet mode '-q'. We only care about the exit code.
if unzip -tq "$DEST_FILE"; then
    echo "ZIP archive test successful! The file is a valid and uncorrupted ZIP archive."
else
    echo "ZIP archive test FAILED! The file is not a valid ZIP archive or is corrupted."
    echo "Deleting downloaded file: $DEST_FILE"
    rm "$DEST_FILE"
    exit 1
fi

echo "========================================"
echo "All verifications passed. File '$DEST_FILE' is ready to use!"

exit 0