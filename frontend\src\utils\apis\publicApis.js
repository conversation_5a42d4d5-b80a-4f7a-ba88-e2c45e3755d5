import axios from "axios";

export const baseURL =
  process.env.NODE_ENV === "development"
    ? localStorage.getItem("nms-setting") === null
      ? "http://localhost:27182"
      : `${JSON.parse(localStorage.getItem("nms-setting")).state.baseURL}`
    : window.location.origin;

export default axios.create({
  baseURL,
  headers: {
    accept: "application/json",
    "Content-Type": "application/json",
  },
});
