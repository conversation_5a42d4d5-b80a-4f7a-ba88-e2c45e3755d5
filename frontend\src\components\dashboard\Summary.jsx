import { Card, Typography, theme } from "antd";
import React from "react";

const Summary = ({ title, headColor, bodyColor, value }) => {
  const token = theme.useToken().token;
  return (
    <Card
      style={{ boxShadow: token?.Card?.boxShadow, textAlign: "center" }}
      variant="borderless"
      title={title}
      styles={{
        header: { backgroundColor: headColor },
        body: { backgroundColor: bodyColor },
      }}
    >
      <Typography.Text strong>{value}</Typography.Text>
    </Card>
  );
};

export default Summary;
