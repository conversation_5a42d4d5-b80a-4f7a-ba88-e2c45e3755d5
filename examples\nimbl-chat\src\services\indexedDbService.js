// src/services/indexedDbService.js
import { openDB } from "idb";

const DB_NAME = "nimbl-ai-db";
const DB_VERSION = 1;
const STORE_NAME = "app-store";

async function getDb() {
  return openDB(DB_NAME, DB_VERSION, {
    upgrade(db) {
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME);
      }
    },
  });
}

export async function setItem(key, value) {
  const db = await getDb();
  return db.put(STORE_NAME, value, key);
}

export async function getItem(key) {
  const db = await getDb();
  return db.get(STORE_NAME, key);
}

export async function removeItem(key) {
  const db = await getDb();
  return db.delete(STORE_NAME, key);
}

export async function clear() {
  const db = await getDb();
  return db.clear(STORE_NAME);
}
