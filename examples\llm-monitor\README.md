# LLM Monitor

A comprehensive monitoring system for collecting and analyzing command and syslog data with AI-powered summaries.

## Pre-requirements

- Python 3.13 or higher
- OpenAI API key (set as environment variable `OPENAI_API_KEY`)
- uv package manager (for dependency management)
- Required Python packages (see pyproject.toml):
  - requests>=2.0
  - openai>=1.0.0
  - python-dotenv>=1.0.0

## Installation

1. Create and activate a virtual environment:
```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

2. Install dependencies using uv:
```bash
uv pip install -e .
```

## Data Collection

### Command Collection
The system uses `cmd_poller.py` to collect command data:
- Polls the API server every 5 minutes (configurable via `POLL_INTERVAL_SEC`)
- Stores command history in `.command_data/command_history.json`
- Generates summaries every 30 minutes (configurable via `SUMMARY_INTERVAL_SEC`)

### Syslog Collection
The system uses `syslog_poller.py` to collect syslog data:
- Monitors system logs in real-time
- Stores syslog data in `.syslog_data/` directory
- Integrates with the summary generation system

## Viewing Summaries

Summaries are available in multiple locations:

1. **Latest Summary**: `.command_data/last_summary.md`
2. **Summary History**: `.command_data/summary_history.json`
3. **System Analysis**: `system_analysis_report.md`
4. **Current Summary**: `summary.md`

## Analyzing Summaries

The `summary_analyzer.py` tool provides several ways to analyze the collected data:

1. **Command Analysis**:
   - Failed commands tracking
   - Client traffic patterns
   - Unverified commands
   - Special attention items

2. **Summary Analysis**:
   - Cumulative statistics
   - Session-based analysis
   - Historical trends
   - AI-powered insights

3. **System Analysis**:
   - Performance metrics
   - Error patterns
   - Security events
   - System health indicators

## Configuration

Key configuration parameters (in `cmd_poller.py` and `syslog_poller.py`):

```python
BASE_URL = "http://localhost:27182"  # API server URL
USERNAME = "admin"                   # Login username
PASSWORD = "default"                 # Login password
POLL_INTERVAL_SEC = 300             # Command polling interval
SUMMARY_INTERVAL_SEC = 1800         # Summary generation interval
```

## Usage

1. Start the command monitor:
```bash
python cmd_poller.py
```

2. Start the syslog monitor:
```bash
python syslog_poller.py
```

3. Generate analysis reports:
```bash
python summary_analyzer.py
```

## Data Storage

- Command data: `.command_data/`
- Syslog data: `.syslog_data/`
- Summary files: Various `.md` files in the root directory

## Notes

- The system maintains a rolling history of the last 1000 command entries
- Summaries are generated using OpenAI's o4-mini model
- All sensitive data is stored locally and not transmitted to external services
