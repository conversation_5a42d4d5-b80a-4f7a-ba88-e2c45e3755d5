#!/usr/bin/env python3
"""
Test script to verify timestamp analysis functionality in detail
"""
import asyncio
import json
import sys
from fastmcp import Client as mcp_client

async def test_timestamp_analysis():
    """Test timestamp analysis functionality"""
    print("Testing MCP timestamp analysis...")
    
    # Configure servers
    servers = {
        "nimble-api-wrapper": {
            "command": "python",
            "args": ["nimbl_mcp.py"]
        }
    }
    
    try:
        # Create client
        print("Creating MCP client...")
        client = mcp_client(servers)
        
        # Connect
        print("Connecting to MCP server...")
        await client._connect()
        print("✓ Connected successfully")
        
        # Test analyze_timestamp tool
        print("\nTesting analyze_timestamp tool...")
        
        # Test cases
        test_cases = [
            {
                "timestamp": "1749702320",  # Recent timestamp 
                "context": "device_last_seen",
                "description": "Recent device timestamp"
            },
            {
                "timestamp": str(int(1749702320) - 3600),  # 1 hour ago
                "context": "device_last_seen", 
                "description": "Device 1 hour ago"
            },
            {
                "timestamp": str(int(1749702320) - 86400),  # 1 day ago
                "context": "command_created",
                "description": "Command from 1 day ago"
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n  Test {i+1}: {test_case['description']}")
            try:
                result = await asyncio.wait_for(
                    client.call_tool("analyze_timestamp", arguments={
                        "timestamp": test_case["timestamp"],
                        "context": test_case["context"]
                    }),
                    timeout=15.0
                )
                
                print(f"    ✓ Analysis successful:")
                print(f"      Relative time: {result.get('relative_time', 'N/A')}")
                print(f"      Status: {result.get('device_status' if 'device' in test_case['context'] else 'command_status', 'N/A')}")
                print(f"      Urgency: {result.get('urgency', 'N/A')}")
                print(f"      Recommendation: {result.get('recommendation', 'N/A')}")
                
            except Exception as e:
                print(f"    ✗ Analysis failed: {e}")
        
        # Test get_commands and show enhanced output
        print(f"\nTesting get_commands with timestamp enhancements...")
        try:
            result = await asyncio.wait_for(
                client.call_tool("get_commands", arguments={}),
                timeout=30.0
            )
            
            if result and 'commands' in result:
                commands = result['commands']
                metadata = result.get('metadata', {})
                
                print(f"✓ Commands retrieved successfully:")
                print(f"  Total commands: {metadata.get('total_commands', 'N/A')}")
                print(f"  Server time: {metadata.get('server_time_human', 'N/A')}")
                
                # Show detailed info for first few commands
                print(f"\n  Command details with timestamp analysis:")
                for i, (cmd_key, cmd_data) in enumerate(commands.items()):
                    if i >= 2:  # Show only first 2 for detail
                        break
                        
                    print(f"\n    Command {i+1}: {cmd_data.get('command', 'N/A')}")
                    print(f"      Status: {cmd_data.get('status', 'N/A')}")
                    print(f"      Created (human): {cmd_data.get('created_human', 'N/A')}")
                    print(f"      Created (UTC): {cmd_data.get('created_utc', 'N/A')}")
                    print(f"      Time Status: {cmd_data.get('time_status', 'N/A')}")
                    print(f"      Urgency Level: {cmd_data.get('urgency_level', 'N/A')}")
                    print(f"      Recommendation: {cmd_data.get('recommendation', 'N/A')}")
                    print(f"      Execution Context: {cmd_data.get('execution_context', 'N/A')}")
                    
                    if 'created_raw' in cmd_data:
                        print(f"      Raw Timestamp: {cmd_data['created_raw']}")
            
        except Exception as e:
            print(f"✗ Commands test failed: {e}")
        
        # Close connection
        print("\nClosing connection...")
        await client.close()
        print("✓ Connection closed")
        
    except Exception as e:
        print(f"✗ Timestamp analysis test failed: {e}")
        return False
    
    return True

def main():
    """Run the test"""
    try:
        success = asyncio.run(test_timestamp_analysis())
        if success:
            print("\n✓ Timestamp analysis test completed successfully")
            sys.exit(0)
        else:
            print("\n✗ Timestamp analysis test failed")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n✗ Test interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n✗ Test error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
