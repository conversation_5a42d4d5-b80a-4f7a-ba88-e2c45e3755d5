import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict
import json
from openai import OpenAI

class SummaryAnalyzer:
    def __init__(self):
        self.command_data_dir = Path(".command_data")
        self.syslog_data_dir = Path(".syslog_data")
        self.analysis_results = {
            "command_analysis": {},
            "syslog_analysis": {},
            "critical_issues": [],
            "potential_threats": [],
            "recommendations": []
        }
        # Initialize OpenAI client
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        if not self.openai_client:
            print("⚠️ Warning: OPENAI_API_KEY not set. LLM analysis will be disabled.")
        
    def analyze_all_summaries(self):
        """Analyze all summary files from both directories."""
        print("\n🔍 Starting comprehensive summary analysis...")
        
        # Analyze command summaries
        if self.command_data_dir.exists():
            print("\n📊 Analyzing command summaries...")
            self._analyze_command_summaries()
        
        # Analyze syslog summaries
        if self.syslog_data_dir.exists():
            print("\n📊 Analyzing syslog summaries...")
            self._analyze_syslog_summaries()
        
        # Generate final report
        self._generate_final_report()
        
        # Perform LLM analysis if available
        if self.openai_client:
            print("\n🤖 Performing LLM analysis...")
            self._perform_llm_analysis()
    
    def _analyze_command_summaries(self):
        """Analyze all command summary files."""
        command_summaries = list(self.command_data_dir.glob("command_summary_*.md"))
        if not command_summaries:
            print("❌ No command summaries found")
            return
        
        # Load summary history for context
        history_file = self.command_data_dir / "summary_history.json"
        if history_file.exists():
            with open(history_file) as f:
                history = json.load(f)
                self.analysis_results["command_analysis"]["history"] = history
        
        # Track patterns across summaries
        failed_commands = defaultdict(list)
        unverified_commands = defaultdict(list)
        client_patterns = defaultdict(lambda: {"total": 0, "failed": 0})
        
        for summary_file in command_summaries:
            with open(summary_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Extract failed commands
                failed_section = self._extract_section(content, "Commands with *status ≠ `ok`*")
                if failed_section:
                    for line in failed_section.split('\n'):
                        if '|' in line and not line.startswith('|'):
                            parts = [p.strip() for p in line.split('|')]
                            if len(parts) >= 5:
                                cmd = parts[3].strip('` ')
                                status = parts[4].strip('` ')
                                client = parts[5].strip('` ')
                                failed_commands[status].append((cmd, client))
                                client_patterns[client]["failed"] += 1
                
                # Extract unverified commands
                unverified_section = self._extract_section(content, "Un-verified commands")
                if unverified_section:
                    for line in unverified_section.split('\n'):
                        if '|' in line and not line.startswith('|'):
                            parts = [p.strip() for p in line.split('|')]
                            if len(parts) >= 4:
                                cmd = parts[3].strip('` ')
                                client = parts[4].strip('` ')
                                unverified_commands[client].append(cmd)
                
                # Extract client traffic
                traffic_section = self._extract_section(content, "Command traffic by client")
                if traffic_section:
                    for line in traffic_section.split('\n'):
                        if '|' in line and not line.startswith('|'):
                            parts = [p.strip() for p in line.split('|')]
                            if len(parts) >= 3:
                                client = parts[1].strip('` ')
                                count = int(parts[2].strip('` '))
                                client_patterns[client]["total"] += count
        
        # Analyze patterns and identify issues
        self._analyze_command_patterns(failed_commands, unverified_commands, client_patterns)
    
    def _analyze_syslog_summaries(self):
        """Analyze all syslog summary files."""
        syslog_summaries = list(self.syslog_data_dir.glob("syslog_summary_*.md"))
        if not syslog_summaries:
            print("❌ No syslog summaries found")
            return
        
        # Load summary history for context
        history_file = self.syslog_data_dir / "summary_history.json"
        if history_file.exists():
            with open(history_file) as f:
                history = json.load(f)
                self.analysis_results["syslog_analysis"]["history"] = history
        
        # Track patterns across summaries
        anomalies = defaultdict(list)
        facility_stats = defaultdict(lambda: {"total": 0, "anomalies": 0})
        host_stats = defaultdict(lambda: {"total": 0, "anomalies": 0})
        
        for summary_file in syslog_summaries:
            with open(summary_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Extract anomalous logs
                anomaly_section = self._extract_section(content, "Anomalous Logs")
                if anomaly_section:
                    for line in anomaly_section.split('\n'):
                        if '|' in line and not line.startswith('|'):
                            parts = [p.strip() for p in line.split('|')]
                            if len(parts) >= 5:
                                severity = parts[2].strip('` ')
                                hostname = parts[3].strip('` ')
                                facility = parts[4].strip('` ')
                                message = parts[5].strip('` ')
                                anomalies[severity].append((hostname, facility, message))
                                host_stats[hostname]["anomalies"] += 1
                
                # Extract facility statistics
                facility_section = self._extract_section(content, "Log Statistics by Facility")
                if facility_section:
                    for line in facility_section.split('\n'):
                        if '|' in line and not line.startswith('|'):
                            parts = [p.strip() for p in line.split('|')]
                            if len(parts) >= 4:
                                facility = parts[1].strip('` ')
                                total = int(parts[2].strip('` '))
                                anomaly_count = int(parts[3].strip('` '))
                                facility_stats[facility]["total"] += total
                                facility_stats[facility]["anomalies"] += anomaly_count
        
        # Analyze patterns and identify issues
        self._analyze_syslog_patterns(anomalies, facility_stats, host_stats)
    
    def _analyze_command_patterns(self, failed_commands: Dict, unverified_commands: Dict, client_patterns: Dict):
        """Analyze patterns in command execution data."""
        # Track critical issues
        critical_issues = []
        
        # Analyze failed commands
        for status, commands in failed_commands.items():
            if len(commands) >= 3:  # Pattern threshold
                critical_issues.append({
                    "type": "recurring_command_failure",
                    "status": status,
                    "count": len(commands),
                    "examples": commands[:3],
                    "severity": "high"
                })
        
        # Analyze unverified commands
        for client, commands in unverified_commands.items():
            if len(commands) >= 2:  # Pattern threshold
                critical_issues.append({
                    "type": "unverified_command_accumulation",
                    "client": client,
                    "count": len(commands),
                    "examples": commands[:3],
                    "severity": "medium"
                })
        
        # Analyze client patterns
        for client, stats in client_patterns.items():
            if stats["total"] > 0:
                failure_rate = (stats["failed"] / stats["total"]) * 100
                if failure_rate > 50:  # High failure rate threshold
                    critical_issues.append({
                        "type": "high_failure_rate",
                        "client": client,
                        "failure_rate": failure_rate,
                        "total_commands": stats["total"],
                        "severity": "high"
                    })
        
        self.analysis_results["command_analysis"]["critical_issues"] = critical_issues
    
    def _analyze_syslog_patterns(self, anomalies: Dict, facility_stats: Dict, host_stats: Dict):
        """Analyze patterns in syslog data."""
        # Track critical issues
        critical_issues = []
        
        # Analyze anomalies by severity
        for severity, logs in anomalies.items():
            if len(logs) >= 3:  # Pattern threshold
                critical_issues.append({
                    "type": "recurring_anomaly",
                    "severity": severity,
                    "count": len(logs),
                    "examples": logs[:3],
                    "severity_level": "high" if severity in ["emerg", "alert", "crit"] else "medium"
                })
        
        # Analyze facility statistics
        for facility, stats in facility_stats.items():
            if stats["total"] > 0:
                anomaly_rate = (stats["anomalies"] / stats["total"]) * 100
                if anomaly_rate > 20:  # High anomaly rate threshold
                    critical_issues.append({
                        "type": "high_anomaly_rate",
                        "facility": facility,
                        "anomaly_rate": anomaly_rate,
                        "total_logs": stats["total"],
                        "severity": "high"
                    })
        
        # Analyze host statistics
        for host, stats in host_stats.items():
            if stats["total"] > 0:
                anomaly_rate = (stats["anomalies"] / stats["total"]) * 100
                if anomaly_rate > 30:  # High anomaly rate threshold
                    critical_issues.append({
                        "type": "host_anomaly_rate",
                        "host": host,
                        "anomaly_rate": anomaly_rate,
                        "total_logs": stats["total"],
                        "severity": "high"
                    })
        
        self.analysis_results["syslog_analysis"]["critical_issues"] = critical_issues
    
    def _extract_section(self, content: str, section_name: str) -> str:
        """Extract a section from markdown content."""
        pattern = f"#### {section_name}.*?---"
        match = re.search(pattern, content, re.DOTALL)
        return match.group(0) if match else ""
    
    def _generate_final_report(self):
        """Generate a comprehensive analysis report."""
        print("\n📋 Generating final analysis report...")
        
        # Combine and deduplicate critical issues
        all_critical_issues = (
            self.analysis_results["command_analysis"].get("critical_issues", []) +
            self.analysis_results["syslog_analysis"].get("critical_issues", [])
        )
        
        # Sort by severity
        severity_order = {"high": 0, "medium": 1, "low": 2}
        all_critical_issues.sort(key=lambda x: severity_order.get(x.get("severity", "low"), 3))
        
        # Generate report
        report = []
        report.append("# System Analysis Report")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # Critical Issues Section
        report.append("## 🔴 Critical Issues Requiring Attention")
        if all_critical_issues:
            for issue in all_critical_issues:
                severity_icon = "🔴" if issue.get("severity") == "high" else "🟡" if issue.get("severity") == "medium" else "🟢"
                report.append(f"\n### {severity_icon} {issue['type'].replace('_', ' ').title()}")
                for key, value in issue.items():
                    if key not in ["type", "severity"]:
                        report.append(f"- **{key.replace('_', ' ').title()}**: {value}")
        else:
            report.append("\nNo critical issues detected.")
        
        # Command Analysis Summary
        report.append("\n## 📊 Command Analysis Summary")
        cmd_history = self.analysis_results["command_analysis"].get("history", {})
        if cmd_history:
            stats = cmd_history.get("cumulative_stats", {})
            report.append(f"- Total commands processed: {stats.get('total_commands_processed', 0)}")
            report.append(f"- Failed commands: {stats.get('total_failed_commands', 0)}")
            report.append(f"- Success rate: {((stats.get('total_commands_processed', 0) - stats.get('total_failed_commands', 0)) / stats.get('total_commands_processed', 1) * 100):.1f}%")
        
        # Syslog Analysis Summary
        report.append("\n## 📊 Syslog Analysis Summary")
        syslog_history = self.analysis_results["syslog_analysis"].get("history", {})
        if syslog_history:
            stats = syslog_history.get("cumulative_stats", {})
            report.append(f"- Total logs processed: {stats.get('total_logs_processed', 0)}")
            report.append(f"- Anomalies detected: {stats.get('total_anomalies', 0)}")
            report.append(f"- Anomaly rate: {(stats.get('total_anomalies', 0) / stats.get('total_logs_processed', 1) * 100):.1f}%")
        
        # Recommendations
        report.append("\n## 💡 Recommendations")
        if all_critical_issues:
            for issue in all_critical_issues:
                if issue.get("severity") == "high":
                    report.append(f"\n### For {issue['type'].replace('_', ' ').title()}:")
                    if issue["type"] == "recurring_command_failure":
                        report.append("- Investigate and fix the root cause of command failures")
                        report.append("- Implement retry mechanisms with exponential backoff")
                        report.append("- Add monitoring for specific failure patterns")
                    elif issue["type"] == "unverified_command_accumulation":
                        report.append("- Implement automated verification processes")
                        report.append("- Set up alerts for unverified commands")
                        report.append("- Establish clear ownership and SLAs for verification")
                    elif issue["type"] == "high_failure_rate":
                        report.append("- Review client configuration and permissions")
                        report.append("- Implement rate limiting and circuit breakers")
                        report.append("- Add detailed logging for failed commands")
                    elif issue["type"] == "recurring_anomaly":
                        report.append("- Investigate system components generating anomalies")
                        report.append("- Implement automated recovery procedures")
                        report.append("- Set up real-time monitoring for anomaly patterns")
                    elif issue["type"] in ["high_anomaly_rate", "host_anomaly_rate"]:
                        report.append("- Review system configuration and health")
                        report.append("- Implement automated health checks")
                        report.append("- Set up proactive monitoring and alerting")
        else:
            report.append("\nNo specific recommendations needed at this time.")
        
        # Save report
        report_path = "system_analysis_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"\n✅ Analysis report generated: {report_path}")
        print("\n📋 Report Summary:")
        print(f"- Critical Issues: {len(all_critical_issues)}")
        print(f"- High Severity: {len([i for i in all_critical_issues if i.get('severity') == 'high'])}")
        print(f"- Medium Severity: {len([i for i in all_critical_issues if i.get('severity') == 'medium'])}")
        print(f"- Low Severity: {len([i for i in all_critical_issues if i.get('severity') == 'low'])}")

    def _perform_llm_analysis(self):
        """Send summary data to LLM for analysis."""
        try:
            # Prepare the data for LLM analysis
            analysis_data = {
                "command_analysis": self.analysis_results["command_analysis"],
                "syslog_analysis": self.analysis_results["syslog_analysis"],
                "critical_issues": self.analysis_results["command_analysis"].get("critical_issues", []) + 
                                 self.analysis_results["syslog_analysis"].get("critical_issues", [])
            }
            
            # Create the prompt for the LLM
            prompt = f"""Please analyze the following system monitoring data and provide insights:

1. Command Analysis:
{json.dumps(analysis_data['command_analysis'], indent=2)}

2. Syslog Analysis:
{json.dumps(analysis_data['syslog_analysis'], indent=2)}

3. Critical Issues:
{json.dumps(analysis_data['critical_issues'], indent=2)}

Please provide:
1. A high-level summary of system health
2. Key patterns and trends identified
3. Potential security concerns
4. Specific recommendations for improvement
5. Areas requiring immediate attention

Format the response in markdown with clear sections and bullet points."""

            # Call the LLM
            response = self.openai_client.chat.completions.create(
                model="gpt-4",  # Using GPT-4 for better analysis
                messages=[
                    {"role": "system", "content": "You are a system monitoring and security analysis expert. Analyze the provided data and give clear, actionable insights."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            # Get the analysis from the response
            llm_analysis = response.choices[0].message.content
            
            # Save the LLM analysis
            llm_report_path = "llm_analysis_report.md"
            with open(llm_report_path, 'w', encoding='utf-8') as f:
                f.write("# LLM Analysis Report\n\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(llm_analysis)
            
            print(f"\n✅ LLM analysis report generated: {llm_report_path}")
            
            # Append LLM analysis to the main report
            with open("system_analysis_report.md", 'a', encoding='utf-8') as f:
                f.write("\n\n## 🤖 LLM Analysis\n\n")
                f.write(llm_analysis)
            
        except Exception as e:
            print(f"❌ Error during LLM analysis: {str(e)}")
            print("Continuing with standard analysis only...")

def main():
    analyzer = SummaryAnalyzer()
    analyzer.analyze_all_summaries()

if __name__ == "__main__":
    main() 