import forwardConfigReducer, { fetchForwardConfig, selectForwardConfig } from "./forwardConfigSlice";
import { configureStore } from "@reduxjs/toolkit";

describe("forwardConfig slice", () => {
	let store;

	beforeEach(() => {
		store = configureStore({
			reducer: { forwardConfig: forwardConfigReducer },
		});
	});

	it("should have initial state", () => {
		const state = store.getState().forwardConfig;
		expect(state).toEqual({ data: null, loading: false, error: null });
	});

	it("should handle fetchForwardConfig.pending", () => {
		const action = { type: fetchForwardConfig.pending.type };
		const nextState = forwardConfigReducer(undefined, action);
		expect(nextState.loading).toBe(true);
		expect(nextState.error).toBeNull();
	});

	it("should handle fetchForwardConfig.fulfilled", () => {
		const sample = { whatsapp: { enabled: true } };
		const action = { type: fetchForwardConfig.fulfilled.type, payload: sample };
		const nextState = forwardConfigReducer(undefined, action);
		expect(nextState.loading).toBe(false);
		expect(nextState.data).toEqual(sample);
	});

	it("should handle fetchForwardConfig.rejected", () => {
		const error = "error message";
		const action = { type: fetchForwardConfig.rejected.type, payload: error };
		const nextState = forwardConfigReducer(undefined, action);
		expect(nextState.loading).toBe(false);
		expect(nextState.error).toBe(error);
	});

	it("selectForwardConfig selector returns slice state", () => {
		const sampleState = { forwardConfig: { data: { a: 1 }, loading: true, error: "err" } };
		const selected = selectForwardConfig(sampleState);
		expect(selected).toEqual(sampleState.forwardConfig);
	});
});
