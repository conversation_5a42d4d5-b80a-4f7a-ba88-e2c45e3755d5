import React, { useLayoutEffect } from "react";
import enUS from "antd/locale/en_US";
import { ThemeProvider } from "antd-style";
import { App as AntdApp, ConfigProvider, Spin, theme } from "antd";
import {
  customDarkAlgorithm,
  customLightAlgorithm,
  useThemeStore,
} from "./utils/themes/useStore";
import GlobalStyle from "./utils/themes/GlobalStyle";
import AppRoutes from "./utils/router/AppRoutes";
import { useDispatch, useSelector } from "react-redux";
import {
  CheckServerStatus,
  serverStatusSelector,
} from "./features/comman/serverStatusSlice";

const App = () => {
  const dispatch = useDispatch();
  const { mode, colorPrimary, baseURL } = useThemeStore();

  useLayoutEffect(() => {
    dispatch(CheckServerStatus());
  }, [baseURL]);

  return (
    <ThemeProvider
      defaultAppearance={mode === "dark" ? "dark" : undefined}
      themeMode={mode}
      theme={(appearance) => ({
        cssVar: true,
        token: {
          colorPrimary,
          borderRadius: 4,
        },
        algorithm:
          appearance === "dark" ? customDarkAlgorithm : customLightAlgorithm,
        components: {},
      })}
    >
      <RouteWrapper />
    </ThemeProvider>
  );
};

export default App;

const RouteWrapper = () => {
  const { loadingServerStatus } = useSelector(serverStatusSelector);
  const { token } = theme.useToken();
  return (
    <ConfigProvider
      locale={enUS}
      theme={{
        inherit: true,
        components: {
          Table: {
            colorFillAlter: token.colorPrimaryBg,
            fontSize: 14,
          },
          Statistic: {
            contentFontSize: 16,
          },
          Button: {
            fontSize: 14,
          },
          Card: {
            boxShadow:
              "rgb(0 0 0 / 20%) 0px 2px 1px -1px, rgb(0 0 0 / 14%) 0px 1px 1px 0px, rgb(0 0 0 / 12%) 0px 1px 3px 0px",
          },
          Descriptions: {
            titleMarginBottom: 0,
          },
          Menu: {
            colorActiveBarWidth: 0,
            itemBg: "transparent",
            subMenuItemBg: "transparent",
            colorSplit: "transparent",
          },
          Badge: {
            fontSizeSM: 16,
          },
        },
      }}
    >
      <GlobalStyle />
      <AntdApp>
        {loadingServerStatus ? (
          <Spin fullscreen tip="Loading..." size="large" />
        ) : (
          <AppRoutes />
        )}
      </AntdApp>
    </ConfigProvider>
  );
};
