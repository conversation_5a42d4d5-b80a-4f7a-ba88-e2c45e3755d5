## Prerequisites
- Node.js and npm must be installed.

- The project must have Cypress and related dependencies installed.

- You must have a valid OpenAI API key to enable AI-assisted test generation.

## How to Set Up Cypress with OpenAI
- Create a .env file in the frontend root directory.

- Add your OpenAI API key in the following format:

CYPRESS_OPENAI_API_KEY=your_openai_api_key

- Add your OpenAI Model in the following format: 

CYPRESS_OPENAI_MODEL=model

- Ensure the application backend is running locally (e.g., at http://localhost:27182).

- If your backend is hosted elsewhere, update the baseUrl in cypress.config.js accordingly.

## Running Cypress Tests
- Open a terminal in the frontend root directory.

- Run npm install

- Run Cypress in interactive mode:

npm run cy:open

- Run Cypress in headless mode:

npm run test:cy
