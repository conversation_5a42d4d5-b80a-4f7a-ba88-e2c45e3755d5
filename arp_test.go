package mnms

import (
	"fmt"
	"testing"
)

func TestArp(t *testing.T) {
	srcip := "***********"
	dstip := "***********0"
	d := NewThirdParty()
	d.enableFilter(srcip, dstip)
	err := scanArp(srcip, dstip, d)
	if err != nil {
		t.Fatal(err)
	}
	for mac, v := range d.dev {
		fmt.Printf("MAC: %s, device ip: %v\n", mac, v.IPAddress)
	}
}

func TestGetCIDRRange(t *testing.T) {
	tests := []struct {
		cidr   string
		start  string
		end    string
		expect bool
	}{
		{"***********/24", "***********", "*************", true},
		{"10.0.0.0/8", "10.0.0.0", "**************", true},
		{"2001:db8::/64", "2001:db8::", "2001:db8::ffff:ffff:ffff:ffff", true},
		{"invalid", "", "", false},
	}

	for _, test := range tests {
		start, end, err := GetCIDRRange(test.cidr)
		if (err == nil) != test.expect {
			t.Errorf("Unexpected error for CIDR %s: %v", test.cidr, err)
			continue
		}
		if err == nil {
			if start.String() != test.start || end.String() != test.end {
				t.Errorf("For CIDR %s, expected start %s and end %s, but got start %s and end %s",
					test.cidr, test.start, test.end, start, end)
			} else {
				fmt.Printf("CIDR %s: Start IP = %s, End IP = %s\n", test.cidr, start, end)
			}
		}
	}
}
