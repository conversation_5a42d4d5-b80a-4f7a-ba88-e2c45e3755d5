import React from "react";
import { Item, Menu } from "react-contexify";
import { createStyles } from "antd-style";
import { Flex } from "antd";

const useStyles = createStyles(({ token, css }) => ({
  contexify: css`
    --contexify-menu-bgColor: ${token.colorBgContainer};
    --contexify-separator-color: #4c4c4c;
    --contexify-item-color: ${token.colorText};
    --contexify-activeItem-bgColor: ${token.colorPrimaryActive};
  `,
}));

const PortInfoContextMenu = ({ onMenuItemClicked }) => {
  const { styles } = useStyles();
  const items = [
    {
      label: "Disable Port",
      key: "disable",
    },
    {
      label: "Enable Port",
      key: "enable",
    },
  ];

  function handleItemClick({ id, event, props, triggerEvent, data }) {
    console.log(id, event, props, triggerEvent, data);
    const { record } = props;
    onMenuItemClicked(id, record);
  }

  return (
    <div>
      <Menu id="port-info-menu" className={styles.contexify}>
        {items.map((item) => (
          <Item id={item.key} onClick={handleItemClick}>
            <Flex gap={10}>{item.label}</Flex>
          </Item>
        ))}
      </Menu>
    </div>
  );
};

export default PortInfoContextMenu;
