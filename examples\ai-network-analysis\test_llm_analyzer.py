#!/usr/bin/env python3
"""
Test script for the updated LLM network analyzer
"""
import asyncio
import os
import json
from pathlib import Path

# Add current directory to path for imports
import sys
sys.path.append(str(Path(__file__).parent))

from llm_network_analyzer import LLMNetworkAnalyzer, NetworkAnalysisConfig

async def test_llm_analyzer():
    """Test the LLM network analyzer with mock API key"""
    print("🧪 Testing LLM Network Analyzer")
    print("=" * 50)
    
    # Test with mock API key first
    config = NetworkAnalysisConfig(
        openai_api_key="test-api-key",
        model="gpt-4o",
        analysis_interval_minutes=30
    )
    
    analyzer = LLMNetworkAnalyzer(config)
    
    # Test 1: Database initialization
    print("📊 Test 1: Database Initialization")
    try:
        analyzer.init_database()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    # Test 2: Raw data collection (without OpenAI)
    print("\n📡 Test 2: Raw Data Collection")
    try:
        raw_data = await analyzer.collect_raw_network_data()
        if raw_data:
            print(f"✅ Raw data collected (snapshot ID: {raw_data.get('snapshot_id', 'N/A')})")
            print(f"   Devices count: {len(raw_data.get('device_data', {}).get('devices', {}))}")
            print(f"   Commands count: {len(raw_data.get('command_data', {}).get('commands', []))}")
        else:
            print("⚠️ No raw data collected (MCP server may not be running)")
    except Exception as e:
        print(f"❌ Raw data collection failed: {e}")
    
    # Test 3: Context retrieval
    print("\n📚 Test 3: Context Retrieval")
    try:
        context = analyzer.get_recent_context()
        print(f"✅ Context retrieved: {context.get('note', 'Has recent data')}")
    except Exception as e:
        print(f"❌ Context retrieval failed: {e}")
    
    # Test 4: Database storage (with mock data)
    print("\n💾 Test 4: Database Storage")
    try:
        mock_device_data = {
            "devices": {
                "device1": {"mac": "00:11:22:33:44:55", "status": "online"},
                "device2": {"mac": "00:11:22:33:44:66", "status": "offline"}
            }
        }
        mock_command_data = {
            "commands": [
                {"id": 1, "command": "ping", "status": "completed"},
                {"id": 2, "command": "reset", "status": "failed"}
            ]
        }
        
        snapshot_id = analyzer.store_raw_snapshot(mock_device_data, mock_command_data)
        print(f"✅ Mock data stored (snapshot ID: {snapshot_id})")
        
        # Test analysis storage
        mock_analysis = {
            "summary": "Test network analysis",
            "key_insights": ["Test insight 1", "Test insight 2"],
            "alerts": ["Test alert"],
            "confidence": 0.85
        }
        
        analyzer.store_llm_analysis(snapshot_id, mock_analysis)
        print("✅ Mock analysis stored")
        
    except Exception as e:
        print(f"❌ Database storage failed: {e}")
    
    # Test 5: Check if real OpenAI API key is available
    print("\n🔑 Test 5: OpenAI API Configuration")
    real_api_key = os.getenv("OPENAI_API_KEY")
    if real_api_key and real_api_key != "your-api-key-here":
        print("✅ OpenAI API key found in environment")
        print("🚀 You can now run the full LLM analysis!")
        
        # Ask if user wants to test with real API
        print("\n🤖 Test with real OpenAI API? (y/n): ", end="")
        # For automated testing, skip the interactive part
        test_real_api = False
        
        if test_real_api:
            print("yes")
            real_config = NetworkAnalysisConfig(
                openai_api_key=real_api_key,
                model="gpt-4o"
            )
            real_analyzer = LLMNetworkAnalyzer(real_config)
            
            print("🔄 Running full analysis cycle with OpenAI...")
            result = await real_analyzer.run_analysis_cycle()
            if result:
                print("🎉 Full LLM analysis completed successfully!")
            else:
                print("❌ Full LLM analysis failed")
        else:
            print("no (skipped for automated testing)")
    else:
        print("⚠️ No OpenAI API key found")
        print("   Set OPENAI_API_KEY environment variable to test with real API")
    
    print("\n🏁 LLM Analyzer testing completed!")
    return True

async def main():
    """Main test function"""
    try:
        success = await test_llm_analyzer()
        if success:
            print("\n🎊 All tests passed!")
        else:
            print("\n💥 Some tests failed!")
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
