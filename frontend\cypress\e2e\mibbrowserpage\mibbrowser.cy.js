describe("MibbrowserPage E2E Tests", () => {
    let ipAddress = "";

  beforeEach(() => {
    cy.visit("/login");
    cy.get('[data-testid="username"]').type("admin");
    cy.get('[data-testid="password"]').type("default");
    cy.get('[data-testid="submit"]').click();
    cy.url().should("not.include", "/login");
        cy.window().then((win) => {
    const token = win.sessionStorage.getItem("nmstoken");
    expect(token).to.exist;

    cy.request({
      method: "GET",
      url: "http://localhost:27182/api/v1/devices",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }).then((res) => {
      expect(res.status).to.eq(200);

      const deviceList = Object.values(res.body);
      const firstValid = deviceList.find((d) => d.ipaddress);

       // ✅ Fail the test immediately if no valid IP is found
      expect(firstValid, "A device with valid IP should exist").to.exist;

      ipAddress = firstValid.ipaddress;
    });
  });

  cy.visit("/mibbrowser");
  });

  it("renders the MibbrowserPage correctly", () => {
    cy.wait(5000);
    cy.contains("Mib Browser").should("exist");
  });

  it("fills in the form and triggers GET operation", () => {
    cy.get("input").eq(0).type(ipAddress);
    cy.get(".ant-select-selector").eq(0).click();
    cy.get(".ant-select-item-option-content").contains("Get").click();
    cy.intercept("POST", "**/GetMibBrowserData").as("mibCommand");
    cy.contains("go").click();
    cy.contains("View Result").click();
  });

  it("fills in the form and triggers Walk operation", () => {
    cy.get("input").eq(0).type(ipAddress); // IP address
    cy.get(".ant-select-selector").eq(0).click();
    cy.get(".ant-select-item-option-content").contains("Walk").click();
    cy.intercept("POST", "**/GetMibBrowserData").as("mibCommand");
    cy.contains("go").click();
    cy.contains("View Result").click();
  });

  it("fills in the form and triggers Bulk operation", () => {
    cy.get("input").eq(0).type(ipAddress); 
    cy.get(".ant-select-selector").eq(0).click();
    cy.get(".ant-select-item-option-content").contains("Bulk").click();
    cy.intercept("POST", "**/GetMibBrowserData").as("mibCommand");
    cy.contains("go").click();
    cy.contains("View Result").click();
  });

 
  it('opens SNMP Set modal when operation is set', () => {
    cy.get('.ant-select-selector').click();
    cy.get(".ant-select-item-option-content").contains("Set").click();
    cy.contains('go').click();
    cy.contains('SNMP Set').should('exist'); 
  });

});
