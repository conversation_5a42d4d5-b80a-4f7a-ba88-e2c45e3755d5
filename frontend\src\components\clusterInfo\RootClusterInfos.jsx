import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  RootClusterInfo,
  clusterInfoSelector,
} from "../../features/clusterInfo/clusterInfoSlice";
import { ProDescriptions } from "@ant-design/pro-components";
import { Card } from "antd";

const RootClusterInfos = () => {
  const { rootInfoData, rootinfofetching } = useSelector(clusterInfoSelector);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(RootClusterInfo());
  }, []);

  return (
    <Card variant="borderless">
      <ProDescriptions
        title="Root Info"
        loading={rootinfofetching}
        dataSource={rootInfoData}
        columns={[
          {
            title: "Name",
            key: "name",
            dataIndex: "name",
          },
          {
            title: "Root url",
            key: "root_url",
            dataIndex: "root_url",
          },
          {
            title: "Port",
            key: "port",
            dataIndex: "port",
          },
          {
            title: "Domain",
            key: "domain",
            dataIndex: "domain",
          },
          {
            title: "Network Services",
            key: "num_clients",
            dataIndex: "num_clients",
          },
          {
            title: "Devices",
            key: "num_devices",
            dataIndex: "num_devices",
          },
          {
            title: "CMD interval",
            key: "cmd_interval",
            dataIndex: "cmd_interval",
          },
          {
            title: "GWD interval",
            key: "gwd_interval",
            dataIndex: "gwd_interval",
          },
          {
            title: "Register interval",
            key: "register_interval",
            dataIndex: "register_interval",
          },
          {
            title: "Remote syslog addr",
            key: "remote_syslog_server_addr",
            dataIndex: "remote_syslog_server_addr",
          },
          {
            title: "Trap server addr",
            key: "trap_server_addr",
            dataIndex: "trap_server_addr",
          },
          {
            title: "Syslog server addr",
            key: "syslog_server_addr",
            dataIndex: "syslog_server_addr",
          },
          {
            title: "Syslog path",
            key: "syslog_local_path",
            dataIndex: "syslog_local_path",
          },
          {
            title: "Syslog file size",
            key: "syslog_file_size",
            dataIndex: "syslog_file_size",
            valueType: "digit",
          },
          {
            title: "Syslog server compress",
            key: "syslog_compress",
            dataIndex: "syslog_compress",
            render: (data) => {
              return data ? "YES" : "NO";
            },
          },
          {
            title: "IDPS license",
            key: "idps_license",
            dataIndex: "idps_license",
            render: (data) => {
              return data ? "YES" : "NO";
            },
          },
          {
            title: "SNMP options",
            key: "snmp_options",
            dataIndex: "snmp_options",
            render: (data) => {
              return JSON.stringify(data, null, 4);
            },
            valueType: "jsonCode",
          },
          {
            title: "License path",
            key: "license_path",
            dataIndex: "licensePath",
          },
          {
            title: "Maximum NIMBL clients",
            key: "max_clients",
            dataIndex: "maxClients",
          },
          {
            title: "Maximum NIMBL devices",
            key: "max_devices",
            dataIndex: "maxDevices",
          },
        ]}
      />
    </Card>
  );
};

export default RootClusterInfos;
