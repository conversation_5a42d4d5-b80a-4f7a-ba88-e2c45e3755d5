//go:build !windows && !ci
// +build !windows,!ci

package ips

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"testing"
	"time"

	"github.com/go-ping/ping"
	"github.com/google/gonids"
)

const bash = "bash"
const c = "-c"

var inputcmdwithoutlo = []string{"iptables -A INPUT ! -i lo -j NFQUEUE --queue-num %v --queue-bypass"}
var outputcmdwithoutlo = []string{"iptables -A OUTPUT ! -o lo -j NFQUEUE --queue-num %v --queue-bypass"}

var dinputcmdwithoutlo = []string{"iptables -D INPUT ! -i lo -j NFQUEUE --queue-num %v --queue-bypass"}
var doutputcmdwithoutlo = []string{"iptables -D OUTPUT ! -o lo -j NFQUEUE --queue-num %v --queue-bypass"}
var outsid_id = defaultid

func cmdNotlo() error {
	for _, cmd := range inputcmdwithoutlo {
		command := fmt.Sprintf(cmd, outsid_id)
		_, err := exec.Command(bash, c, command).Output()
		if err != nil {
			return err
		}
	}
	for _, cmd := range outputcmdwithoutlo {
		command := fmt.Sprintf(cmd, outsid_id)
		_, err := exec.Command(bash, c, command).Output()
		if err != nil {
			return err
		}
	}
	time.Sleep(time.Second * 1)
	return nil
}

func cleancmdNotlo() error {
	for _, cmd := range dinputcmdwithoutlo {
		command := fmt.Sprintf(cmd, outsid_id)
		_, err := exec.Command(bash, c, command).Output()
		if err != nil {
			return err
		}
	}
	for _, cmd := range doutputcmdwithoutlo {
		command := fmt.Sprintf(cmd, outsid_id)
		_, err := exec.Command(bash, c, command).Output()
		if err != nil {
			return err
		}
	}
	time.Sleep(time.Second * 1)
	return nil
}

var inputcmdwithlo = []string{"iptables -A INPUT -i lo -j NFQUEUE --queue-num %v --queue-bypass"}
var outputcmdwithtlo = []string{"iptables -A OUTPUT -o lo -j NFQUEUE --queue-num %v --queue-bypass"}

var dinputcmdwithlo = []string{"iptables -D INPUT -i lo -j NFQUEUE --queue-num %v --queue-bypass"}
var doutputcmdwithlo = []string{"iptables -D OUTPUT -o lo -j NFQUEUE --queue-num %v --queue-bypass"}
var lo_id = defaultid + 1

func cmdwithlo() error {

	for _, cmd := range inputcmdwithlo {
		command := fmt.Sprintf(cmd, lo_id)
		_, err := exec.Command(bash, c, command).Output()
		if err != nil {
			return err
		}
	}
	for _, cmd := range outputcmdwithtlo {
		command := fmt.Sprintf(cmd, lo_id)
		_, err := exec.Command(bash, c, command).Output()
		if err != nil {
			return err
		}
	}
	time.Sleep(time.Second * 1)
	return nil
}

func cleancmdwithlo() error {
	for _, cmd := range dinputcmdwithlo {
		command := fmt.Sprintf(cmd, lo_id)
		_, err := exec.Command(bash, c, command).Output()
		if err != nil {
			return err
		}
	}
	for _, cmd := range doutputcmdwithlo {
		command := fmt.Sprintf(cmd, lo_id)
		_, err := exec.Command(bash, c, command).Output()
		if err != nil {
			return err
		}
	}
	time.Sleep(time.Second * 1)
	return nil
}

func TestMain(m *testing.M) {
	cleancmdNotlo()
	cleancmdwithlo()
	err := cmdNotlo()
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
	err = cmdwithlo()
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
	defer cleancmdNotlo()
	defer cleancmdwithlo()
	m.Run()
}

func pingTest(ip string, count int, timeout time.Duration) (int, error) {
	var err error
	c := 0
	cmd := exec.Command("sudo", "sysctl", "-w", "net.ipv4.ping_group_range=0 2147483647")
	err = cmd.Run()
	if err != nil {
		return 0, err
	}
	pinger, err := ping.NewPinger(ip)
	if err != nil {
		return c, err
	}
	pinger.Count = count
	pinger.OnRecv = func(pkt *ping.Packet) {
		fmt.Printf("%d bytes from %s: icmp_seq=%d time=%v\n",
			pkt.Nbytes, pkt.IPAddr, pkt.Seq, pkt.Rtt)
		c++
	}
	go func() {
		err = pinger.Run()
	}()
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	for range ctx.Done() {
		return c, err
	}
	return c, nil
}
func TestAddDropRule(t *testing.T) {
	ip := "*******"
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}

	ips := f.(*UIps)
	ips.id = outsid_id
	id := RandUint16()
	v := fmt.Sprintf(`drop icmp %v any -> $HOME_NET any (msg:"test";sid:%v;)`, ip, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	c, err := pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if c != 0 {
		t.Fatal("should drop arp")
	}

}

func TestDropExternalRule(t *testing.T) {
	ip := "*******"
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}

	ips := f.(*UIps)
	ips.id = outsid_id
	id := RandUint16()
	v := fmt.Sprintf(`drop icmp $EXTERNAL_NET any -> $HOME_NET any (msg:"test";sid:%v;)`, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	c, err := pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if c != 0 {
		t.Fatal("should drop arp")
	}

}

func TestAdddiffRule(t *testing.T) {

	ip := "*******"
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	ips := f.(*UIps)
	ips.id = outsid_id
	id := RandUint16()
	v := fmt.Sprintf(`pass icmp %v any -> $HOME_NET %v (msg:"test";sid:%v;)`, ip, 23, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	id = RandUint16()
	v = fmt.Sprintf(`pass icmp %v any -> $HOME_NET %v (msg:"test";sid:%v;)`, ip, 23, id)
	r, err = gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}

	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}

}

func TestDropStartAndClose(t *testing.T) {
	ip := "*******"
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	ips := f.(*UIps)
	ips.id = outsid_id
	id := RandUint16()
	v := fmt.Sprintf(`drop icmp %v any -> $HOME_NET any (msg:"test";sid:%v;)`, ip, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	c, err := pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if c != 0 {
		t.Fatal("should drop arp")
	}
	time.Sleep(time.Second * 1)

	c, err = pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if c != 0 {
		t.Fatal("should drop arp")
	}

}

var receivecount = 0
var port = 4096
var localip = "127.0.0.1"
var addr = localip + ":" + fmt.Sprint(port)

func TestDropPayloadRule(t *testing.T) {

	receivecount = 0
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	ips := f.(*UIps)
	ips.id = lo_id
	id := RandUint16()
	v := fmt.Sprintf(`drop udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"zxc";sid:%v;)`, port, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	payload := "zxccvb123456789"
	c := 10

	go udp()

	time.Sleep(time.Second * 1)
	for i := 0; i < c; i++ {
		sendUDP(addr, payload)
	}
	defer listen.Close()
	time.Sleep(time.Second * 1)
	if receivecount != 0 {
		t.Fatal(fmt.Errorf("expect receice count:%v,actual:%v", 0, receivecount))
	}

}

func TestDropMutiplecontentRule(t *testing.T) {

	receivecount = 0
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	ips := f.(*UIps)
	ips.id = lo_id
	id := RandUint16()
	v := fmt.Sprintf(`drop udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"zxc";nocase;content:"abc";nocase;;sid:%v;)`, port, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	c := 10

	go udp()
	time.Sleep(time.Second * 1)
	for i := 0; i < c; i++ {
		payload := "ZXCcvb123abc456789"
		sendUDP(addr, payload)
	}

	for i := 0; i < c; i++ {
		payload := "ABCcvb123zxc456789"
		sendUDP(addr, payload)
	}
	time.Sleep(time.Second * 1)
	defer listen.Close()
	if receivecount != 0 {
		t.Fatal(fmt.Errorf("expect receice count:%v,actual:%v", 0, receivecount))
	}

}

func TestDropOffSetPayloadRule(t *testing.T) {
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	ips := f.(*UIps)
	ips.id = lo_id
	id := RandUint16()
	v := fmt.Sprintf(`drop udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"cvb123";offset:3;sid:%v;)`, port, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	payload := "zxccvb123456789"
	c := 10

	go udp()

	time.Sleep(time.Second * 1)
	for i := 0; i < c; i++ {
		sendUDP(addr, payload)
	}

	time.Sleep(time.Second * 1)
	defer listen.Close()
	if receivecount != 0 {
		t.Fatal(fmt.Errorf("expect receice count:%v,actual:%v", 0, receivecount))
	}

}

func TestAcceptPayloadRule(t *testing.T) {
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	ips := f.(*UIps)
	ips.id = lo_id
	id := RandUint16()
	v := fmt.Sprintf(`pass udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"zxc";sid:%v;)`, port, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	payload := "zxccvb123456789"
	c := 10

	go udp()

	time.Sleep(time.Second * 1)
	for i := 0; i < c; i++ {
		sendUDP(addr, payload)
	}
	time.Sleep(time.Second * 1)
	defer listen.Close()
	if receivecount != c {
		t.Fatal(fmt.Errorf("expect receice count:%v,actual:%v", c, receivecount))
	}

}

var listen net.PacketConn

func udp() {
	var err error
	listen, err = net.ListenPacket("udp", addr)
	if err != nil {
		return
	}
	for {
		var data [1024]byte
		n, addr, err := listen.ReadFrom(data[:])
		if err != nil {
			continue
		}
		receivecount++
		log.Printf("data:%v addr:%v,receivecount:%v", string(data[:n]), addr, receivecount)
	}
}

func sendUDP(addr, msg string) (string, error) {
	conn, _ := net.Dial("udp", addr)
	// send to socket
	_, err := conn.Write([]byte(msg))
	return "", err
}
