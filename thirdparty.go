package mnms

import (
	"bytes"
	_ "embed"
	"fmt"
	"net"
	"strings"
	"sync"

	"github.com/google/gopacket/layers"
	"github.com/klauspost/oui"
)

//go:embed oui.txt
var b []byte
var ouidb oui.StaticDB

func init() {
	db, _ := oui.OpenStatic(bytes.NewReader(b))
	ouidb = db
}

func NewThirdParty() *thirdPartydev {
	return &thirdPartydev{dev: make(map[string]DevInfo), m: new(sync.Mutex)}
}

type thirdPartydev struct {
	filtered bool
	srip     net.IP
	endip    net.IP
	m        *sync.Mutex
	dev      map[string]DevInfo
}

var atopMacPrefix = "00-60-E9"

// add others device of Vendor, not include atop devices
func (a *thirdPartydev) add(d layers.ARP) {
	a.m.Lock()
	defer a.m.Unlock()
	if a.filtered && !a.isIPInRange(d.SourceProtAddress, a.srip, a.endip) {
		return
	}
	mac := byteToHexString(d.Source<PERSON>w<PERSON>dd<PERSON>, "-")
	if !strings.HasPrefix(mac, atopMacPrefix) {
		dev := DevInfo{}
		dev.Mac = mac
		dev.IPAddress = byteToString(d.SourceProtAddress, ".")
		if ouidb != nil {
			entry, _ := ouidb.Query(mac)
			if entry != nil {
				dev.Hostname = entry.Manufacturer
			}
		}

		dev.ScannedBy = QC.Name
		dev.IsOnline = true
		dev.Scanproto = "arp"
		dev.Type = "IP discovered"
		dev.Tag = "third party"
		a.dev[dev.Mac] = dev

	}
}
func (a *thirdPartydev) enableFilter(scrip, endip string) error {
	a.srip = net.ParseIP(scrip)
	if a.srip == nil {
		return fmt.Errorf("invalid start IP: %s", scrip)
	}
	a.endip = net.ParseIP(endip)
	if a.endip == nil {
		return fmt.Errorf("invalid end IP: %s", endip)
	}
	a.filtered = true
	return nil
}

func (a *thirdPartydev) isIPInRange(ip, start, end net.IP) bool {
	return ipInRange(ip, start, end)
}

func ipInRange(ip, start, end net.IP) bool {
	ip = ip.To4()
	start = start.To4()
	end = end.To4()
	if ip == nil || start == nil || end == nil {
		return false
	}
	return bytesCompare(ip, start) >= 0 && bytesCompare(ip, end) <= 0
}

func bytesCompare(a, b net.IP) int {
	for i := 0; i < len(a); i++ {
		if a[i] < b[i] {
			return -1
		} else if a[i] > b[i] {
			return 1
		}
	}
	return 0
}
