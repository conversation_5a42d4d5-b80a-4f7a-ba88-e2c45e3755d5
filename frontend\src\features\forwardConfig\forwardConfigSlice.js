import { createAsyncThunk, createSlice, createSelector } from "@reduxjs/toolkit";
import protectedApis from "../../utils/apis/protectedApis";

export const fetchForwardConfig = createAsyncThunk(
	"forwardConfig/fetchForwardConfig",
	async (_, thunkAPI) => {
		try {
			const response = await protectedApis.get("/api/v1/forwards");
			const data = response.data;
			if (response.status === 200) {
				return data;
			} else {
				return thunkAPI.rejectWithValue("Fetch forward config failed");
			}
		} catch (e) {
			if (e.response && e.response.statusText) {
				return thunkAPI.rejectWithValue(e.response.statusText);
			}
			return thunkAPI.rejectWithValue(e.message);
		}
	}
);

const forwardConfigSlice = createSlice({
	name: "forwardConfig",
	initialState: {
		data: null,
		loading: false,
		error: null,
		lastUpdated: null,
	},
	reducers: {
		updateConfig: (state, action) => {
			state.data = action.payload;
			state.lastUpdated = Date.now();
		},
		clearError: (state) => {
			state.error = null;
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(fetchForwardConfig.pending, (state) => {
				state.loading = true;
				state.error = null;
			})
			.addCase(fetchForwardConfig.fulfilled, (state, action) => {
				state.loading = false;
				state.data = action.payload;
				state.lastUpdated = Date.now();
			})
			.addCase(fetchForwardConfig.rejected, (state, action) => {
				state.loading = false;
				state.error = action.payload || action.error.message;
			});
	},
});

export default forwardConfigSlice.reducer;

// Export actions
export const { updateConfig, clearError } = forwardConfigSlice.actions;

export const selectForwardConfig = createSelector(
	(state) => state.forwardConfig,
	(forwardConfig) => forwardConfig
);
