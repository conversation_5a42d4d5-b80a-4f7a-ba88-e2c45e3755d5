/* Style for Mermaid container */
.mermaid-container {
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  min-height: 100px;
  transition: background-color 0.3s, border-color 0.3s;
}

[data-mui-color-scheme="light"] .mermaid-container {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}

[data-mui-color-scheme="dark"] .mermaid-container {
  background-color: #2f2f2f;
  border: 1px solid #444;
}

.mermaid-container svg {
  display: block;
  margin: auto;
  max-width: 100%;
  height: auto;
}

[data-mui-color-scheme="dark"] .mermaid-container svg {
  filter: brightness(0.9);
}

/* Style for Mermaid nodes and edges in dark mode */
[data-mui-color-scheme="dark"] .mermaid-container .node rect,
[data-mui-color-scheme="dark"] .mermaid-container .node circle,
[data-mui-color-scheme="dark"] .mermaid-container .node polygon,
[data-mui-color-scheme="dark"] .mermaid-container .node path {
  stroke: #666 !important;
}

[data-mui-color-scheme="dark"] .mermaid-container .edgePath .path {
  stroke: #666 !important;
}

[data-mui-color-scheme="dark"] .mermaid-container .label {
  color: #fff !important;
}

/* Loading state */
.mermaid-placeholder {
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
  text-align: center;
  font-family: "Roboto Mono", monospace;
  white-space: pre-wrap;
  font-size: 14px;
  transition: background-color 0.3s, border-color 0.3s;
}

[data-mui-color-scheme="light"] .mermaid-placeholder {
  background-color: #fafafa;
  border: 2px dashed #bdbdbd;
  color: #757575;
}

[data-mui-color-scheme="dark"] .mermaid-placeholder {
  background-color: #2f2f2f;
  border: 2px dashed #555;
  color: #aaa;
}
