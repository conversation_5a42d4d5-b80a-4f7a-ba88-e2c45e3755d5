"""
LLM-powered network analysis that builds contextual summaries over time
Uses OpenAI API to analyze raw network data and maintain historical context
Stores analysis results as timestamped JSON files for easy management and inspection
Designed for one-shot analysis - use external scheduling (cron, Task Scheduler) for automation
"""
import asyncio
import json
import os
import glob
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path
from openai import AsyncOpenAI
from fastmcp import Client as mcp_client
import httpx

@dataclass
class NetworkAnalysisConfig:
    """Configuration for LLM-powered network analysis"""
    openai_api_key: str
    model: str = "gpt-4o"
    reports_dir: str = "network_reports"
    summary_retention_days: int = 30
    max_raw_data_size: int = 30000  # characters - reduced for API stability
    api_timeout: int = 120  # seconds
    max_tokens: int = 2000

class LLMNetworkAnalyzer:
    """LLM-powered network analysis with JSON file-based historical context"""
    def __init__(self, config: NetworkAnalysisConfig):
        self.config = config
        self.reports_dir = Path(config.reports_dir)
        self.mcp_servers = {
            "nimble-api-wrapper": {
                "command": "python",
                "args": ["./nimbl_mcp.py"]
            }
        }
        self.init_reports_directory()
        
        # Configure OpenAI client with timeout settings
        http_client = httpx.AsyncClient(
            timeout=httpx.Timeout(
                timeout=config.api_timeout,
                connect=30.0,
                read=config.api_timeout,
                write=30.0,
                pool=10.0
            ),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
        
        self.openai_client = AsyncOpenAI(
            api_key=config.openai_api_key,
            http_client=http_client
        )
    
    def init_reports_directory(self):
        """Initialize directory structure for storing analysis reports"""
        self.reports_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for different types of reports
        (self.reports_dir / "snapshots").mkdir(exist_ok=True)
        (self.reports_dir / "analysis").mkdir(exist_ok=True)
        (self.reports_dir / "summaries").mkdir(exist_ok=True)
        
        print(f"[INFO] Reports directory initialized: {self.reports_dir.absolute()}")
    async def collect_raw_network_data(self) -> Dict[str, Any]:
        """Collect raw network data from NIMBL MCP server"""
        try:
            client = mcp_client(self.mcp_servers)
            await client._connect()
            
            # Get raw device data
            devices_result = await client.call_tool("get_devices", arguments={})
            device_data = json.loads(devices_result[0].text) if devices_result else {}
            
            # Get raw command data
            commands_result = await client.call_tool("get_commands", arguments={})
            command_data = json.loads(commands_result[0].text) if commands_result else {}
            
            await client.close()
            
            # Create timestamp-based snapshot
            timestamp = datetime.now()
            snapshot_data = {
                "timestamp": timestamp.isoformat(),
                "collection_info": {
                    "devices_count": len(device_data.get("devices", {})),
                    "commands_count": len(command_data.get("commands", [])),
                    "data_size": len(json.dumps(device_data)) + len(json.dumps(command_data))
                },
                "device_data": device_data,
                "command_data": command_data
            }
            
            # Save raw snapshot to file
            snapshot_file = self.save_snapshot(snapshot_data, timestamp)
            
            return {
                "timestamp": timestamp.isoformat(),
                "snapshot_file": snapshot_file,
                "device_data": device_data,
                "command_data": command_data
            }
            
        except Exception as e:
            print(f"[ERROR] Data collection failed: {e}")
            return {}
    
    def save_snapshot(self, snapshot_data: Dict[str, Any], timestamp: datetime) -> str:
        """Save raw network data snapshot to timestamped JSON file"""
        filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_snapshot.json"
        filepath = self.reports_dir / "snapshots" / filename
        
        with open(filepath, 'w') as f:
            json.dump(snapshot_data, f, indent=2)
        
        print(f"[SAVED] Snapshot: {filename}")
        return str(filepath)    
    
    async def analyze_with_llm(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send raw network data to LLM for intelligent analysis"""
        try:
            # Get previous context for continuity
            previous_context = self.get_recent_context()
            
            # Detect device configuration changes
            device_changes = self.detect_device_changes(raw_data["device_data"])
            
            # Prepare LLM prompt with raw data
            system_prompt = """You are a network management expert analyzing NIMBL network data. 
Analyze the raw network data and provide insights about device health, command patterns, 
potential issues, and recommendations. Focus on temporal patterns and anomalies.

Pay special attention to device configuration changes, as these may indicate:
- Planned maintenance or updates
- Security concerns (unauthorized changes)
- Network infrastructure modifications
- Device replacements or upgrades

Respond with JSON format:
{
  "summary": "Brief overall network status",
  "key_insights": ["insight1", "insight2", ...],
  "device_analysis": "Analysis of device status and patterns",
  "command_analysis": "Analysis of command execution patterns",
  "configuration_changes": "Analysis of any device configuration changes detected", 
  "alerts": ["alert1", "alert2", ...],
  "recommendations": ["rec1", "rec2", ...],
  "confidence": 0.95,
  "trends_detected": ["trend1", "trend2", ...]
}"""
            
            # Prepare data for LLM - with better size management
            data_for_llm = {
                "current_snapshot": {
                    "timestamp": raw_data["timestamp"],
                    "devices": raw_data["device_data"],
                    "commands": raw_data["command_data"]
                },
                "previous_context": previous_context,
                "device_changes": device_changes
            }
            
            # Smart data truncation to fit within API limits
            data_str = json.dumps(data_for_llm)
            print(f"[DEBUG] Initial data size: {len(data_str)} characters")
            
            if len(data_str) > self.config.max_raw_data_size:
                print(f"[INFO] Data too large ({len(data_str)} chars), truncating...")
                
                # Step 1: Limit device data
                devices = raw_data["device_data"].get("devices", {})
                if len(devices) > 5:
                    # Keep only 5 most recent devices
                    device_items = list(devices.items())
                    truncated_devices = dict(device_items[:5])
                    data_for_llm["current_snapshot"]["devices"]["devices"] = truncated_devices
                    data_for_llm["truncation_note"] = f"Device data truncated: showing 5 of {len(devices)} devices"
                
                # Step 2: Limit command data
                commands = raw_data["command_data"].get("commands", {})
                if len(commands) > 10:
                    command_items = list(commands.items())
                    truncated_commands = dict(command_items[:10])
                    data_for_llm["current_snapshot"]["commands"]["commands"] = truncated_commands
                    if "truncation_note" not in data_for_llm:
                        data_for_llm["truncation_note"] = f"Command data truncated: showing 10 of {len(commands)} commands"
                    else:
                        data_for_llm["truncation_note"] += f"; Command data truncated: showing 10 of {len(commands)} commands"
                
                # Step 3: Simplify previous context if still too large
                data_str = json.dumps(data_for_llm)
                if len(data_str) > self.config.max_raw_data_size:
                    data_for_llm["previous_context"] = {"note": "Previous context truncated for size"}
                
                final_size = len(json.dumps(data_for_llm))
                print(f"[INFO] Data truncated to {final_size} characters")
            
            user_prompt = f"""Analyze this network data:
            {json.dumps(data_for_llm, indent=2)}

Focus on:
1. Device temporal patterns (last seen times, status changes)
2. Command execution trends and failures
3. Potential network issues or anomalies
4. Device configuration changes (hostnames, IPs, versions, status changes)
5. Comparisons with previous analysis if available
6. Actionable recommendations for network administrators

Pay special attention to any device configuration changes listed in 'device_changes' section."""
            
            print(f"[API] Sending request to OpenAI ({self.config.model})...")
            
            # Call OpenAI API with timeout and retry logic
            retry_count = 0
            max_retries = 3
            
            while retry_count < max_retries:
                try:
                    response = await self.openai_client.chat.completions.create(
                        model=self.config.model,
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        temperature=0.3,
                        max_tokens=self.config.max_tokens,
                        timeout=self.config.api_timeout
                    )
                    break  # Success, exit retry loop
                    
                except Exception as api_error:
                    retry_count += 1
                    print(f"[WARNING] API attempt {retry_count} failed: {str(api_error)}")
                    
                    if retry_count >= max_retries:
                        raise api_error
                    
                    # Wait before retry
                    import asyncio
                    await asyncio.sleep(5 * retry_count)  # Exponential backoff
            
            print(f"[SUCCESS] OpenAI API response received")
            
            # Parse LLM response
            llm_response = response.choices[0].message.content
            
            # Handle JSON wrapped in markdown code blocks
            if llm_response.strip().startswith('```'):
                # Extract JSON from markdown code blocks
                lines = llm_response.strip().split('\n')
                start_idx = 0
                end_idx = len(lines)
                
                # Find start of JSON (skip ```json line)
                for i, line in enumerate(lines):
                    if line.startswith('```'):
                        start_idx = i + 1
                        break
                
                # Find end of JSON (find closing ```)
                for i in range(len(lines) - 1, -1, -1):
                    if lines[i].startswith('```'):
                        end_idx = i
                        break
                
                # Join the JSON lines
                json_content = '\n'.join(lines[start_idx:end_idx])
            else:
                json_content = llm_response
            
            try:
                analysis_result = json.loads(json_content)
            except json.JSONDecodeError:
                # Fallback if LLM doesn't return valid JSON
                analysis_result = {
                    "summary": "Analysis completed with parsing issues",
                    "raw_response": llm_response,
                    "confidence": 0.5
                }
              # Store analysis result
            analysis_file = self.save_analysis_result(raw_data, analysis_result)
            analysis_result["analysis_file"] = analysis_file
            return analysis_result
            
        except Exception as e:
            print(f"[ERROR] LLM analysis failed: {e}")
            print(f"[ERROR] Error type: {type(e).__name__}")
            
            # Provide more specific error handling
            error_details = {
                "error": str(e),
                "error_type": type(e).__name__,
                "confidence": 0.0,
                "summary": "Analysis failed due to API error"
            }
            
            # Check for specific error types
            if "timeout" in str(e).lower():
                error_details["error_details"] = "API request timed out. Try reducing data size or checking network connectivity."
            elif "rate" in str(e).lower() or "quota" in str(e).lower():
                error_details["error_details"] = "API rate limit exceeded. Please wait before retrying."
            elif "authentication" in str(e).lower() or "unauthorized" in str(e).lower():
                error_details["error_details"] = "API authentication failed. Check your OpenAI API key."
            else:
                error_details["error_details"] = "Unexpected API error occurred."
            
            return error_details
    
    def save_analysis_result(self, raw_data: Dict[str, Any], analysis: Dict[str, Any]) -> str:
        """Save LLM analysis results to timestamped JSON file"""
        timestamp = datetime.fromisoformat(raw_data["timestamp"]) if isinstance(raw_data["timestamp"], str) else datetime.now()
        filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_analysis.json"
        filepath = self.reports_dir / "analysis" / filename
        
        analysis_report = {
            "metadata": {
                "timestamp": raw_data["timestamp"],
                "analysis_type": "network_health",
                "snapshot_file": raw_data.get("snapshot_file", ""),
                "model_used": self.config.model,
                "created_at": datetime.now().isoformat()
            },
            "analysis": analysis,
            "raw_data_summary": {
                "devices_count": len(raw_data.get("device_data", {}).get("devices", {})),
                "commands_count": len(raw_data.get("command_data", {}).get("commands", []))
            }
        }
        
        with open(filepath, 'w') as f:
            json.dump(analysis_report, f, indent=2)
        
        print(f"[SAVED] Analysis: {filename}")
        return str(filepath)    
    def get_recent_context(self, days: int = 1) -> Dict[str, Any]:
        """Get recent analysis context from JSON files for LLM continuity"""
        try:
            since_date = datetime.now() - timedelta(days=days)
            analysis_files = []
            
            # Find recent analysis files
            pattern = str(self.reports_dir / "analysis" / "*_analysis.json")
            for filepath in glob.glob(pattern):
                filename = Path(filepath).name
                # Extract timestamp from filename (YYYYMMDD_HHMMSS_analysis.json)
                timestamp_str = filename.split('_')[0] + '_' + filename.split('_')[1]
                try:
                    file_date = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                    if file_date > since_date:
                        analysis_files.append((filepath, file_date))
                except ValueError:
                    continue  # Skip files with invalid timestamp format
            
            # Sort by date (newest first) and take last 3
            analysis_files.sort(key=lambda x: x[1], reverse=True)
            recent_files = analysis_files[:3]
            
            if not recent_files:
                return {"note": "No recent analysis available"}
            
            context = {
                "recent_summaries": [],
                "recurring_insights": [],
                "persistent_alerts": []
            }
            
            for filepath, file_date in recent_files:
                try:
                    with open(filepath, 'r') as f:
                        report = json.load(f)
                    
                    analysis = report.get("analysis", {})
                    context["recent_summaries"].append({
                        "timestamp": report.get("metadata", {}).get("timestamp", file_date.isoformat()),
                        "summary": analysis.get("summary", "")
                    })
                    
                    # Collect insights and alerts
                    insights = analysis.get("key_insights", [])
                    alerts = analysis.get("alerts", [])
                    context["recurring_insights"].extend(insights)
                    context["persistent_alerts"].extend(alerts)
                    
                except Exception as e:
                    print(f"[WARNING] Failed to read analysis file {filepath}: {e}")
                    continue
            
            return context
            
        except Exception as e:
            print(f"[ERROR] Failed to get recent context: {e}")
            return {"note": "Error retrieving recent analysis"}
    
    def detect_device_changes(self, current_devices: Dict[str, Any]) -> Dict[str, Any]:
        """Detect changes in device configuration compared to previous snapshot"""
        try:
            # Find the most recent snapshot file (excluding current one if it exists)
            pattern = str(self.reports_dir / "snapshots" / "*_snapshot.json")
            snapshot_files = []
            
            for filepath in glob.glob(pattern):
                filename = Path(filepath).name
                timestamp_str = filename.split('_')[0] + '_' + filename.split('_')[1]
                try:
                    file_date = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                    snapshot_files.append((filepath, file_date))
                except ValueError:
                    continue
            
            # Sort by date and get the second most recent (first is likely current)
            snapshot_files.sort(key=lambda x: x[1], reverse=True)
            
            if len(snapshot_files) < 2:
                return {"note": "No previous snapshot available for comparison"}
            
            previous_file = snapshot_files[1][0]  # Second most recent
            
            with open(previous_file, 'r') as f:
                previous_snapshot = json.load(f)
            
            previous_devices = previous_snapshot.get("device_data", {}).get("devices", {})
            current_device_list = current_devices.get("devices", {})
            
            changes = {
                "new_devices": [],
                "removed_devices": [],
                "modified_devices": [],
                "configuration_changes": [],
                "comparison_timestamp": previous_snapshot.get("timestamp", "unknown")
            }
            
            # Check for new devices
            for mac, device_data in current_device_list.items():
                if mac not in previous_devices:
                    changes["new_devices"].append({
                        "mac": mac,
                        "device_info": device_data
                    })
            
            # Check for removed devices
            for mac in previous_devices:
                if mac not in current_device_list:
                    changes["removed_devices"].append({
                        "mac": mac,
                        "last_info": previous_devices[mac]
                    })
            
            # Check for configuration changes in existing devices
            for mac, current_info in current_device_list.items():
                if mac in previous_devices:
                    previous_info = previous_devices[mac]
                    device_changes = []
                    
                    # Compare key device attributes
                    comparable_fields = ["hostname", "ip", "modelname", "softwareversion", 
                                       "hardwareversion", "macaddr", "isonline", "snmpip"]
                    
                    for field in comparable_fields:
                        current_value = current_info.get(field)
                        previous_value = previous_info.get(field)
                        
                        if current_value != previous_value:
                            device_changes.append({
                                "field": field,
                                "previous": previous_value,
                                "current": current_value
                            })
                    
                    if device_changes:
                        changes["modified_devices"].append({
                            "mac": mac,
                            "changes": device_changes
                        })
                        
                        # Create human-readable change descriptions
                        for change in device_changes:
                            if change["field"] == "hostname":
                                changes["configuration_changes"].append(
                                    f"Device {mac}: Hostname changed from '{change['previous']}' to '{change['current']}'"
                                )
                            elif change["field"] == "ip":
                                changes["configuration_changes"].append(
                                    f"Device {mac}: IP address changed from '{change['previous']}' to '{change['current']}'"
                                )
                            elif change["field"] == "isonline":
                                status = "online" if change["current"] else "offline"
                                prev_status = "online" if change["previous"] else "offline"
                                changes["configuration_changes"].append(
                                    f"Device {mac}: Status changed from {prev_status} to {status}"
                                )
                            elif change["field"] in ["softwareversion", "hardwareversion"]:
                                changes["configuration_changes"].append(
                                    f"Device {mac}: {change['field']} updated from '{change['previous']}' to '{change['current']}'"
                                )
                            else:
                                changes["configuration_changes"].append(
                                    f"Device {mac}: {change['field']} changed from '{change['previous']}' to '{change['current']}'"
                                )
            
            return changes
            
        except Exception as e:
            return {"error": f"Failed to detect changes: {e}"}
    async def generate_historical_summary(self) -> Dict[str, Any]:
        """Generate accumulated historical summary using LLM based on JSON files"""
        try:
            # Get last week of analyses from JSON files
            week_ago = datetime.now() - timedelta(days=7)
            historical_data = []
            
            pattern = str(self.reports_dir / "analysis" / "*_analysis.json")
            for filepath in glob.glob(pattern):
                filename = Path(filepath).name
                timestamp_str = filename.split('_')[0] + '_' + filename.split('_')[1]
                try:
                    file_date = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                    if file_date > week_ago:
                        with open(filepath, 'r') as f:
                            report = json.load(f)
                        historical_data.append((report, file_date))
                except (ValueError, json.JSONDecodeError, FileNotFoundError) as e:
                    print(f"[WARNING] Skipping file {filepath}: {e}")
                    continue
            
            if not historical_data:
                return {"error": "No historical data available"}
            
            # Sort by date
            historical_data.sort(key=lambda x: x[1])
            
            # Prepare data for LLM trend analysis
            summaries_for_llm = []
            for report, file_date in historical_data:
                analysis = report.get("analysis", {})
                summaries_for_llm.append({
                    "timestamp": report.get("metadata", {}).get("timestamp", file_date.isoformat()),
                    "summary": analysis.get("summary", ""),
                    "insights": analysis.get("key_insights", []),
                    "alerts": analysis.get("alerts", []),
                    "confidence": analysis.get("confidence", 0.0)
                })
            
            system_prompt = """You are analyzing a week of network management data summaries. 
Identify trends, patterns, recurring issues, and provide strategic recommendations.

Respond with JSON:
{
  "trend_summary": "Overall trends observed",
  "recurring_issues": ["issue1", "issue2", ...],
  "improvement_areas": ["area1", "area2", ...],
  "strategic_recommendations": ["rec1", "rec2", ...],
  "network_health_trend": "improving|stable|declining",
  "key_metrics": {"devices_avg": 10, "alerts_per_day": 2}
}"""
            
            user_prompt = f"""Analyze these network summaries from the past week:

{json.dumps(summaries_for_llm, indent=2)}

Identify:
1. Trending patterns in device health
2. Recurring network issues  
3. Command execution patterns
4. Areas needing attention
5. Strategic recommendations for network improvement"""            
            
            print(f"[API] Generating historical summary with OpenAI...")
            
            # Call OpenAI API with retry logic
            retry_count = 0
            max_retries = 3
            
            while retry_count < max_retries:
                try:
                    response = await self.openai_client.chat.completions.create(
                        model=self.config.model,
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        temperature=0.2,
                        max_tokens=1500,
                        timeout=self.config.api_timeout
                    )
                    break  # Success, exit retry loop
                    
                except Exception as api_error:
                    retry_count += 1
                    print(f"[WARNING] Historical summary API attempt {retry_count} failed: {str(api_error)}")
                    
                    if retry_count >= max_retries:
                        raise api_error
                    
                    # Wait before retry
                    import asyncio
                    await asyncio.sleep(5 * retry_count)
            print(f"[SUCCESS] Historical summary API response received")
            
            llm_response = response.choices[0].message.content
            
            # Handle JSON wrapped in markdown code blocks
            if llm_response.strip().startswith('```'):
                lines = llm_response.strip().split('\n')
                start_idx = 0
                end_idx = len(lines)
                
                for i, line in enumerate(lines):
                    if line.startswith('```'):
                        start_idx = i + 1
                        break
                
                for i in range(len(lines) - 1, -1, -1):
                    if lines[i].startswith('```'):
                        end_idx = i
                        break
                
                json_content = '\n'.join(lines[start_idx:end_idx])
            else:
                json_content = llm_response
            
            try:
                trend_analysis = json.loads(json_content)
            except json.JSONDecodeError as e:
                print(f"[WARNING] JSON parsing failed for historical summary: {e}")
                trend_analysis = {
                    "trend_summary": "Analysis completed with parsing issues",
                    "raw_response": llm_response,
                    "error": f"JSON parsing failed: {e}"
                }
            
            # Store historical summary
            summary_file = self.save_historical_summary(trend_analysis)
            trend_analysis["summary_file"] = summary_file
            
            return trend_analysis
            
        except Exception as e:
            print(f"[ERROR] Historical summary generation failed: {e}")
            return {"error": str(e)}
    
    def save_historical_summary(self, summary: Dict[str, Any]) -> str:
        """Save historical trend summary to timestamped JSON file"""
        timestamp = datetime.now()
        filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_weekly_summary.json"
        filepath = self.reports_dir / "summaries" / filename
        
        summary_report = {
            "metadata": {
                "timestamp": timestamp.isoformat(),
                "summary_type": "weekly_trends",
                "model_used": self.config.model,
                "created_at": timestamp.isoformat()
            },
            "summary": summary
        }
        
        with open(filepath, 'w') as f:
            json.dump(summary_report, f, indent=2)
        
        print(f"[SAVED] Historical Summary: {filename}")
        return str(filepath)
    async def run_analysis_cycle(self):
        """Run complete analysis cycle: collect -> analyze -> summarize"""
        print("[INFO] Starting LLM-powered network analysis cycle...")
        
        # Step 1: Collect raw data
        print("[DATA] Collecting raw network data...")
        raw_data = await self.collect_raw_network_data()
        
        if not raw_data:
            print("[ERROR] Failed to collect network data")
            return
        print(f"[SUCCESS] Collected data snapshot: {raw_data.get('snapshot_file', 'N/A')}")
        
        # Step 2: LLM analysis
        print("[LLM] Analyzing with LLM...")
        analysis = await self.analyze_with_llm(raw_data)
        
        if "error" in analysis:
            print(f"[ERROR] LLM analysis failed: {analysis['error']}")
            return
        
        print("[SUCCESS] LLM analysis completed")
        print(f"[SUMMARY] {analysis.get('summary', 'N/A')}")
        print(f"[SAVED] Analysis report: {analysis.get('analysis_file', 'N/A')}")
        
        # Display configuration changes
        config_changes = analysis.get("configuration_changes", "")
        if config_changes and config_changes != "None":
            print(f"[CONFIG] Configuration Changes: {config_changes}")
        
        # Display key insights
        insights = analysis.get("key_insights", [])
        if insights:
            print("[INSIGHTS] Key Insights:")
            for insight in insights[:3]:  # Show top 3
                print(f"   - {insight}")
        
        # Display alerts
        alerts = analysis.get("alerts", [])
        if alerts:
            print("[ALERTS] Alerts Generated:")
            for alert in alerts:
                print(f"   ! {alert}")
          # Step 3: Generate historical summary (weekly)
        if datetime.now().weekday() == 0:  # Monday
            print("[TREND] Generating weekly trend analysis...")
            historical = await self.generate_historical_summary()
            if "error" not in historical:
                print("[SUCCESS] Historical summary updated")
                print(f"[SAVED] Summary report: {historical.get('summary_file', 'N/A')}")
                print(f"[TREND] {historical.get('network_health_trend', 'N/A')}")
        
        return analysis

# Configuration and usage
async def main():
    """Main function to run LLM network analyzer"""
    
    # Configuration - get from environment variable
    api_key = os.getenv("OPENAI_API_KEY")    
    if not api_key or api_key == "your-api-key-here":
        print("[ERROR] Please set OPENAI_API_KEY environment variable")
        print("[EXAMPLE] OPENAI_API_KEY=sk-your-key python llm_network_analyzer.py")
        return
        
    config = NetworkAnalysisConfig(
        openai_api_key=api_key,
        model="gpt-4o"
    )
    
    analyzer = LLMNetworkAnalyzer(config)
      # Run single analysis cycle
    result = await analyzer.run_analysis_cycle()
    if result:
        print("\n[SUCCESS] Analysis cycle completed successfully!")
        print("[INFO] Use 'python view_reports.py' to manage reports and generate summaries:")
        print("   - 'python view_reports.py list' - View all available reports")
        print("   - 'python view_reports.py latest' - Show latest analysis")
        print("   - 'python view_reports.py summary' - Generate weekly summary")
        print("   - 'python view_reports.py cleanup' - Remove old reports")
        
        return True
    else:
        print("\n[ERROR] Analysis cycle failed")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())