import React from "react";
import {
  Box,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  IconButton,
  Typography,
  Divider,
  Paper,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Chat as ChatIcon,
} from "@mui/icons-material";
import SessionFileManager from "./SessionFileManager";
import { exportSessionsToFile } from "../services/fileService";

const Sidebar = ({
  sessions,
  activeSessionId,
  onSelectSession,
  onNewSession,
  onDeleteSession,
  mode,
  filename,
  onFilenameChange,
  onImportSessions,
}) => {
  const handleExport = () => {
    exportSessionsToFile(sessions, filename);
  };

  return (
    <Paper
      elevation={0}
      sx={{
        width: 300,
        bgcolor: mode === "dark" ? "#202123" : "#f8f9fa",
        color: mode === "dark" ? "white" : "text.primary",
        display: "flex",
        flexDirection: "column",
        height: "100vh",
      }}
    >
      <Box sx={{ p: 2 }}>
        <Button
          variant="outlined"
          fullWidth
          startIcon={<AddIcon />}
          onClick={onNewSession}
          disabled={!filename}
          sx={{
            color: mode === "dark" ? "white" : "text.primary",
            borderColor:
              mode === "dark"
                ? "rgba(255, 255, 255, 0.3)"
                : "rgba(0, 0, 0, 0.23)",
            "&:hover": {
              borderColor:
                mode === "dark"
                  ? "rgba(255, 255, 255, 0.5)"
                  : "rgba(0, 0, 0, 0.5)",
              bgcolor:
                mode === "dark"
                  ? "rgba(255, 255, 255, 0.1)"
                  : "rgba(0, 0, 0, 0.04)",
            },
          }}
        >
          New Session
        </Button>
      </Box>

      <Divider
        sx={{
          borderColor: mode === "dark" ? "rgba(255, 255, 255, 0.1)" : "divider",
        }}
      />

      <Box sx={{ flexGrow: 1, overflow: "auto" }}>
        <List sx={{ p: 1 }}>
          {sessions.map((session) => (
            <ListItem key={session.id} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                selected={session.id === activeSessionId}
                onClick={() => onSelectSession(session.id)}
                sx={{
                  borderRadius: 1,
                  color: mode === "dark" ? "white" : "text.primary",
                  "&.Mui-selected": {
                    bgcolor: mode === "dark" ? "#444654" : "action.selected",
                    "&:hover": {
                      bgcolor: mode === "dark" ? "#444654" : "action.selected",
                    },
                  },
                  "&:hover": {
                    bgcolor: mode === "dark" ? "#343541" : "action.hover",
                  },
                }}
              >
                <ChatIcon
                  sx={{
                    mr: 1,
                    fontSize: 20,
                    color:
                      mode === "dark"
                        ? "rgba(255, 255, 255, 0.7)"
                        : "action.active",
                  }}
                />
                <ListItemText
                  primary={
                    <Typography
                      variant="body2"
                      variantMapping={{ body2: "div" }}
                      sx={{
                        fontWeight: 500,
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {session.name || "Untitled Session"}
                    </Typography>
                  }
                  secondary={
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <Typography
                        variant="caption"
                        variantMapping={{ caption: "span" }}
                        sx={{
                          color: mode === "dark" ? "#acacbe" : "text.secondary",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          pr: 1,
                        }}
                      >
                        {session.provider}: {session.model}
                      </Typography>
                      <Typography
                        variant="caption"
                        variantMapping={{ caption: "span" }}
                        sx={{
                          color: mode === "dark" ? "#acacbe" : "text.secondary",
                          flexShrink: 0,
                        }}
                      >
                        {session.messages?.length || 0} msgs
                      </Typography>
                    </Box>
                  }
                />
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteSession(session.id);
                  }}
                  sx={{
                    color: mode === "dark" ? "#acacbe" : "action.active",
                    opacity: 0,
                    transition: "opacity 0.2s",
                    ".MuiListItemButton-root:hover &": {
                      opacity: 1,
                    },
                    "&:hover": {
                      color: mode === "dark" ? "white" : "text.primary",
                    },
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>

      <Divider
        sx={{
          borderColor: mode === "dark" ? "rgba(255, 255, 255, 0.1)" : "divider",
        }}
      />

      <SessionFileManager
        filename={filename}
        onFilenameChange={onFilenameChange}
        onImport={onImportSessions}
        onExport={handleExport}
        disableFilenameInput={sessions.length > 0}
      />
    </Paper>
  );
};

export default Sidebar;
