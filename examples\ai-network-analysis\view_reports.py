#!/usr/bin/env python3
"""
Simple utility to view and manage JSON-based network analysis reports
"""
import json
import glob
import os
from datetime import datetime
from pathlib import Path

def list_reports(reports_dir="network_reports"):
    """List all available reports by type"""
    reports_path = Path(reports_dir)
    
    if not reports_path.exists():
        print(f"[INFO] Reports directory '{reports_dir}' does not exist yet.")
        print("Run the analyzer first to generate reports.")
        return
    
    print(f"[INFO] Reports in {reports_path.absolute()}:")
    print()
    
    # List snapshots
    snapshots = glob.glob(str(reports_path / "snapshots" / "*_snapshot.json"))
    snapshots.sort(reverse=True)  # Most recent first
    print(f"📸 SNAPSHOTS ({len(snapshots)} files):")
    for filepath in snapshots[:5]:  # Show last 5
        filename = Path(filepath).name
        timestamp_str = filename.replace('_snapshot.json', '').replace('_', ' ')
        try:
            file_date = datetime.strptime(filename.split('_')[0] + '_' + filename.split('_')[1], '%Y%m%d_%H%M%S')
            print(f"   {file_date.strftime('%Y-%m-%d %H:%M:%S')} - {filename}")
        except ValueError:
            print(f"   {filename}")
    if len(snapshots) > 5:
        print(f"   ... and {len(snapshots) - 5} more")
    print()
    
    # List analyses
    analyses = glob.glob(str(reports_path / "analysis" / "*_analysis.json"))
    analyses.sort(reverse=True)
    print(f"🔍 ANALYSES ({len(analyses)} files):")
    for filepath in analyses[:5]:
        filename = Path(filepath).name
        try:
            file_date = datetime.strptime(filename.split('_')[0] + '_' + filename.split('_')[1], '%Y%m%d_%H%M%S')
            print(f"   {file_date.strftime('%Y-%m-%d %H:%M:%S')} - {filename}")
        except ValueError:
            print(f"   {filename}")
    if len(analyses) > 5:
        print(f"   ... and {len(analyses) - 5} more")
    print()
      # List summaries
    summaries = glob.glob(str(reports_path / "summaries" / "*_summary.json"))
    summaries.sort(reverse=True)
    print(f"📊 SUMMARIES ({len(summaries)} files):")
    for filepath in summaries:
        filename = Path(filepath).name
        try:
            file_date = datetime.strptime(filename.split('_')[0] + '_' + filename.split('_')[1], '%Y%m%d_%H%M%S')
            print(f"   {file_date.strftime('%Y-%m-%d %H:%M:%S')} - {filename}")
        except ValueError:
            print(f"   {filename}")

def view_latest_analysis(reports_dir="network_reports"):
    """View the latest analysis report"""
    analyses = glob.glob(str(Path(reports_dir) / "analysis" / "*_analysis.json"))
    if not analyses:
        print("[INFO] No analysis reports found.")
        return
    
    # Get most recent
    analyses.sort(reverse=True)
    latest_file = analyses[0]
    
    try:
        with open(latest_file, 'r') as f:
            report = json.load(f)
        
        print(f"[LATEST ANALYSIS] {Path(latest_file).name}")
        print("=" * 50)
        
        metadata = report.get("metadata", {})
        analysis = report.get("analysis", {})
        
        print(f"Timestamp: {metadata.get('timestamp', 'N/A')}")
        print(f"Model: {metadata.get('model_used', 'N/A')}")
        print()
        
        print(f"Summary: {analysis.get('summary', 'N/A')}")
        print()
        
        insights = analysis.get("key_insights", [])
        if insights:
            print("Key Insights:")
            for i, insight in enumerate(insights[:3], 1):
                print(f"  {i}. {insight}")
            print()
        
        alerts = analysis.get("alerts", [])
        if alerts:
            print("Alerts:")
            for i, alert in enumerate(alerts, 1):
                print(f"  ! {alert}")
            print()
        
        config_changes = analysis.get("configuration_changes", "")
        if config_changes and config_changes != "None":
            print(f"Configuration Changes: {config_changes}")
            print()
        
        print(f"Confidence: {analysis.get('confidence', 'N/A')}")
        
    except Exception as e:
        print(f"[ERROR] Failed to read report: {e}")

def cleanup_old_reports(reports_dir="network_reports", days=30):
    """Clean up reports older than specified days"""
    reports_path = Path(reports_dir)
    if not reports_path.exists():
        print(f"[INFO] Reports directory '{reports_dir}' does not exist.")
        return
    
    cutoff_date = datetime.now().timestamp() - (days * 24 * 60 * 60)
    deleted_count = 0
    
    for pattern in ["snapshots/*_snapshot.json", "analysis/*_analysis.json", "summaries/*_weekly_summary.json"]:
        files = glob.glob(str(reports_path / pattern))
        for filepath in files:
            file_stat = os.stat(filepath)
            if file_stat.st_mtime < cutoff_date:
                try:
                    os.remove(filepath)
                    deleted_count += 1
                    print(f"[DELETED] {Path(filepath).name}")
                except Exception as e:
                    print(f"[ERROR] Failed to delete {filepath}: {e}")
    
    print(f"[INFO] Cleaned up {deleted_count} old reports (older than {days} days)")

def generate_summary(reports_dir="network_reports"):
    """Generate a summary from available analysis reports"""
    reports_path = Path(reports_dir)
    analyses = glob.glob(str(reports_path / "analysis" / "*_analysis.json"))
    
    if len(analyses) < 1:
        print("[INFO] Need at least 1 analysis report to generate summary.")
        return
    
    analyses.sort(reverse=True)
    print(f"[INFO] Generating summary from {len(analyses)} analysis reports...")
    
    try:
        summaries = []
        device_counts = []
        alert_trends = []
        confidence_scores = []
        
        for filepath in analyses:
            with open(filepath, 'r') as f:
                report = json.load(f)
            
            analysis = report.get("analysis", {})
            metadata = report.get("metadata", {})
            
            summaries.append({
                "timestamp": metadata.get("timestamp", ""),
                "summary": analysis.get("summary", ""),
                "insights": analysis.get("key_insights", []),
                "alerts": analysis.get("alerts", []),
                "confidence": analysis.get("confidence", 0.0)
            })
            
            # Collect metrics
            raw_data = report.get("raw_data_summary", {})
            device_counts.append(raw_data.get("devices_count", 0))
            alert_trends.append(len(analysis.get("alerts", [])))
            confidence_scores.append(analysis.get("confidence", 0.0))
        
        # Generate summary report
        summary_report = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "summary_type": "on_demand_summary",
                "reports_analyzed": len(analyses),
                "date_range": f"{summaries[-1]['timestamp']} to {summaries[0]['timestamp']}"
            },
            "summary": {
                "trend_summary": f"Analyzed {len(analyses)} network snapshots. Average {sum(device_counts)/len(device_counts):.1f} devices tracked.",
                "recurring_issues": [
                    "Device connectivity issues appear consistently",
                    "Command execution errors for missing devices",
                    "Stale device status indicating intermittent connectivity"
                ],
                "key_metrics": {
                    "avg_devices": sum(device_counts) / len(device_counts),
                    "avg_alerts_per_analysis": sum(alert_trends) / len(alert_trends),
                    "avg_confidence": sum(confidence_scores) / len(confidence_scores),
                    "total_analyses": len(analyses)
                },
                "network_health_trend": "stable" if sum(alert_trends) / len(alert_trends) < 5 else "needs_attention",
                "strategic_recommendations": [
                    "Investigate devices with consistent connectivity issues",
                    "Review network infrastructure for intermittent failures",
                    "Consider device health monitoring improvements",
                    "Schedule regular device maintenance cycles"
                ]
            },
            "detailed_timeline": summaries[:10]  # Last 10 analyses
        }
        
        # Save summary file
        timestamp = datetime.now()
        filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_manual_summary.json"
        filepath = reports_path / "summaries" / filename
        
        # Ensure summaries directory exists
        filepath.parent.mkdir(exist_ok=True)
        
        with open(filepath, 'w') as f:
            json.dump(summary_report, f, indent=2)
        
        print(f"[SUCCESS] Summary generated: {filename}")
        print(f"[SAVED] Summary saved to: summaries/{filename}")
        print()
        
        # Display summary
        summary = summary_report["summary"]
        print("📊 GENERATED SUMMARY")
        print("=" * 40)
        print(f"Reports Analyzed: {len(analyses)}")
        print(f"Date Range: {summary_report['metadata']['date_range']}")
        print()
        print(f"Trend Summary: {summary['trend_summary']}")
        print()
        print("Key Metrics:")
        metrics = summary["key_metrics"]
        print(f"  • Average Devices: {metrics['avg_devices']:.1f}")
        print(f"  • Average Alerts: {metrics['avg_alerts_per_analysis']:.1f}")
        print(f"  • Average Confidence: {metrics['avg_confidence']:.2f}")
        print(f"  • Network Health: {summary['network_health_trend']}")
        print()
        print("Strategic Recommendations:")
        for rec in summary["strategic_recommendations"]:
            print(f"  • {rec}")
        
    except Exception as e:
        print(f"[ERROR] Failed to generate summary: {e}")

def main():
    import sys
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python view_reports.py list                    - List all reports")
        print("  python view_reports.py latest                  - View latest analysis")
        print("  python view_reports.py summary                 - Generate summary from analyses")
        print("  python view_reports.py cleanup [days]          - Clean up old reports (default: 30 days)")
        return
    
    command = sys.argv[1]
    
    if command == "list":
        list_reports()
    elif command == "latest":
        view_latest_analysis()
    elif command == "summary":
        generate_summary()
    elif command == "cleanup":
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 30        
        cleanup_old_reports(days=days)
    elif command == "summary":
        generate_summary()
    else:
        print(f"Unknown command: {command}")

if __name__ == "__main__":
    main()
