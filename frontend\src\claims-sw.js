import {
  cleanupOutdatedCaches,
  createHandlerBoundToURL,
  precacheAndRoute,
} from "workbox-precaching";
import { clientsClaim } from "workbox-core";
import { NavigationRoute, registerRoute } from "workbox-routing";

// Add error handling for service worker messages
self.addEventListener("message", (event) => {
  try {
    // Handle any messages sent to the service worker
    console.log("Service Worker received message:", event.data);

    // Always respond to prevent "message channel closed" errors
    if (event.ports && event.ports[0]) {
      event.ports[0].postMessage({ success: true });
    }
  } catch (error) {
    console.warn("Service Worker message handling error:", error);
    // Still try to respond to prevent channel closure
    if (event.ports && event.ports[0]) {
      event.ports[0].postMessage({ success: false, error: error.message });
    }
  }
});

// Add error handling for unhandled promise rejections in service worker
self.addEventListener("unhandledrejection", (event) => {
  console.warn("Service Worker unhandled promise rejection:", event.reason);
  event.preventDefault(); // Prevent the error from propagating
});

// self.__WB_MANIFEST is default injection point
precacheAndRoute(self.__WB_MANIFEST);

// clean old assets
cleanupOutdatedCaches();

let allowlist;
if (import.meta.env.DEV) allowlist = [/^\/$/];

// to allow work offline
registerRoute(
  new NavigationRoute(createHandlerBoundToURL("index.html"), { allowlist })
);

self.skipWaiting();
clientsClaim();
