#!/usr/bin/env python3
"""
Test script to demonstrate device change detection in LLM Network Analyzer
"""
import asyncio
import json
import os
from pathlib import Path

# Add current directory to path for imports
import sys
sys.path.append(str(Path(__file__).parent))

from llm_network_analyzer import LLMNetworkAnalyzer, NetworkAnalysisConfig

async def test_device_change_detection():
    """Test device change detection functionality"""
    print("🧪 Testing Device Change Detection")
    print("=" * 50)
    
    # Set up analyzer
    api_key = "***************************************************"
    config = NetworkAnalysisConfig(
        openai_api_key=api_key,
        model="gpt-4o",
        analysis_interval_minutes=30
    )
    
    analyzer = LLMNetworkAnalyzer(config)
    
    print("📊 Test 1: Run baseline analysis")
    result1 = await analyzer.run_analysis_cycle()
    print("✅ Baseline analysis completed")
    
    # Wait a moment to ensure different timestamps
    await asyncio.sleep(2)
    
    print("\n📊 Test 2: Run second analysis (should detect any changes)")
    result2 = await analyzer.run_analysis_cycle()
    
    if result2:
        print("✅ Change detection analysis completed")
        
        # Check if configuration changes were detected
        config_changes = result2.get("configuration_changes", "")
        if config_changes and config_changes != "None":
            print(f"\n🔄 Configuration Changes Detected:")
            print(f"   {config_changes}")
        else:
            print("\n📋 No configuration changes detected between snapshots")
    
    return True

async def main():
    """Main test function"""
    try:
        success = await test_device_change_detection()
        if success:
            print("\n🎉 Device change detection test completed!")
        else:
            print("\n❌ Test failed!")
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")

if __name__ == "__main__":
    asyncio.run(main())
