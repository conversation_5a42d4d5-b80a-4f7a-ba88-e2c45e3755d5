/// <reference types="cypress" />

const extractJSON = (text) => {
  const match = text.match(/{[\s\S]*}/);
  if (match) return JSON.parse(match[0]);
  throw new Error("Failed to extract JSON from AI response");
};
describe('User Management Tests', () => {
    const adminUser = {
      name: 'admin',
      role: 'admin',
      email: '<EMAIL>'
    };
  
    // Add base URL configuration
    before(() => {
      Cypress.config('baseUrl', 'http://localhost:3000'); 
    });
  
    beforeEach(() => {
      cy.intercept('GET', '**/api/v1/users**', { fixture: 'users.json' }).as('getUsers');
      
      // Set session storage values
      cy.window().then((win) => {
        win.sessionStorage.setItem('nmsuserrole', 'admin');
        win.sessionStorage.setItem('nmsuser', 'admin');
        win.sessionStorage.setItem('sessionid', 'mock-session-id');
      });
      
      cy.visit('/login');
      cy.get('[data-testid="username"]').type('admin');
        cy.get('[data-testid="password"]').type('default');
        cy.get('[data-testid="submit"]').click();
        cy.visit('/usermanagement');

    });
  
    it('should display the users table with correct columns', () => {
      // Wait for either the API call or table render
      cy.get('.ant-pro-table').should('exist');
      cy.contains('Username').should('be.visible');
      cy.contains('Email').should('be.visible');
      cy.contains('Role').should('be.visible');
      cy.contains('Two Factor Auth').should('be.visible');
      
      // Optional: Add timeout for API call
      cy.wait('@getUsers', { timeout: 10000 }).then((interception) => {
        assert.isNotNull(interception.response.body, 'API call completed');
      });
  
      it('should display users data correctly', () => {
        cy.fixture('users.json').then((users) => {
          users.forEach((user, index) => {
            cy.get(`tbody tr:nth-child(${index + 1}) td:nth-child(1)`).should('contain', user.name);
            cy.get(`tbody tr:nth-child(${index + 1}) td:nth-child(2)`).should('contain', user.email);
            cy.get(`tbody tr:nth-child(${index + 1}) td:nth-child(3)`).should('contain', user.role);
          });
        });
      });
  
      it('should allow searching users', () => {
        cy.get('.ant-pro-table-search input').type('admin');
        cy.get('tbody tr').should('have.length', 1);
        cy.contains('admin').should('exist');
      });
    });
  
    context('User CRUD Operations', () => {
      it('should open add user modal', () => {
        cy.contains('Add New').click();
        cy.get('.ant-modal-title').should('contain', 'Add new user');
      });
  
    it('should create a new user', () => {
        // Mock the API response for user creation
        cy.intercept('POST', '**/api/v1/users', {
          statusCode: 200,
          body: { message: 'User created successfully' }
        }).as('createUser');
    
        // Open the add user modal
        cy.contains('Add New').click();
        
        // Wait for modal animation and form rendering
        cy.get('.ant-modal-content', { timeout: 10000 }).should('be.visible');
    
        // Fill the form using data-testid attributes (recommended)
        cy.get('[data-testid="username"]').type('newuser');
        cy.get('[data-testid="email"]').type('<EMAIL>');
        cy.get('[data-testid="password"]').type('Test@1234');
        
        // Select role dropdown
        cy.get('[data-testid="role"]').click();
        cy.get('.ant-select-item-option-content').contains('User').click();
    
        // Submit the form
        cy.get('[data-testid="submit-button"]').click();
    
        // Verify API call
        cy.wait('@createUser').its('request.body').should('deep.equal', {
          email: '<EMAIL>',
          name: 'newuser',
          password: 'Test@1234',
          role: 'user'
        });
    
        // Verify success notification
        cy.contains('User has been added').should('be.visible');
      });
    });
  
    context('Two-Factor Authentication', () => {
      it('should show 2FA toggle for admin users', () => {
        cy.get('.ant-switch').first().should('exist');
      });
    });
  
    context('Authorization Checks', () => {
      it('should hide admin actions for regular users', () => {
        cy.window().then((win) => {
          win.sessionStorage.setItem('nmsuserrole', 'user');
        });
        
        cy.reload();
        cy.contains('Add New').should('not.exist');
        cy.get('[aria-label="edit"]').should('not.exist');
        cy.get('[aria-label="delete"]').should('not.exist');
      });
    });
    context("AI-Generated User Management Logic Tests", () => {
  it("should register AI-generated user", () => {
    cy.task("callOpenAI", {
      prompt: `Give a valid user object in JSON for testing a user creation form. Format: { "name": "john_doe", "email": "<EMAIL>", "password": "Secret@123", "role": "user" }`,
    }).then((aiData) => {
      const { name, email, password, role } = extractJSON(aiData);

      cy.intercept("POST", "**/api/v1/users", {
        statusCode: 200,
        body: { message: "User created successfully" },
      }).as("aiCreateUser");

      cy.contains("Add New").click();
      cy.get(".ant-modal-content").should("be.visible");

      cy.get('[data-testid="username"]').type(name);
      cy.get('[data-testid="email"]').type(email);
      cy.get('[data-testid="password"]').type(password);
      cy.get('[data-testid="role"]').click();
      cy.get(".ant-select-item-option-content").contains(new RegExp(role, "i")).click();
      cy.get('[data-testid="submit-button"]').click();

      cy.wait("@aiCreateUser").its("request.body").should("deep.include", {
        name,
        email,
        password,
        role,
      });

      cy.contains("User has been added").should("exist");
    });
  });

  it("should reject user with AI-generated invalid role", () => {
    cy.task("callOpenAI", {
      prompt: `Suggest an invalid role in a JSON format for a user form. Format: { "role": "root-super-admin" }`,
    }).then((aiData) => {
      const { role } = extractJSON(aiData);

      cy.contains("Add New").click();
      cy.get(".ant-modal-content").should("be.visible");

      cy.get('[data-testid="username"]').type("testInvalidRole");
      cy.get('[data-testid="email"]').type("<EMAIL>");
      cy.get('[data-testid="password"]').type("Test@1234");

      // Try to force invalid role input if allowed
      cy.get('[data-testid="role"]').click();
      cy.get(".ant-select-dropdown").within(() => {
        cy.get(".ant-select-item-option-content").should("not.contain", role);
      });

      cy.get('[data-testid="submit-button"]').click();
      cy.contains("Please select the role!").should("exist");
    });
  });

  it("should trigger validation on AI-suggested bad email", () => {
    cy.task("callOpenAI", {
      prompt: `Suggest a badly formatted email in JSON: { "email": "john@@@example" }`,
    }).then((aiData) => {
      const { email } = extractJSON(aiData);

      cy.contains("Add New").click();
      cy.get('[data-testid="username"]').type("badEmailUser");
      cy.get('[data-testid="email"]').type(email);
      cy.get('[data-testid="password"]').type("Test@1234");
      cy.get('[data-testid="submit-button"]').click();

      cy.contains("The input is not valid E-mail!").should("exist");
    });
  });

  it("should check AI-expected password strength error", () => {
    cy.task("callOpenAI", {
      prompt: `Give a weak password that a form should reject. Format: { "password": "12345" }`,
    }).then((aiData) => {
      const { password } = extractJSON(aiData);

      cy.contains("Add New").click();
      cy.get('[data-testid="username"]').type("weakpass");
      cy.get('[data-testid="email"]').type("<EMAIL>");
      cy.get('[data-testid="password"]').type(password);
      cy.get('[data-testid="submit-button"]').click();

      cy.contains("password must have 8-20 characters, at least one uppercase one lowercase one digit one special character").should("exist");
    });
  });

  it("should describe form structure via AI", () => {
    const structure = {
      fields: ["username", "email", "password", "role"],
      roleOptions: ["admin", "user", "auditor"],
      requiresValidation: ["email", "password"],
    };

    cy.task("callOpenAI", {
      prompt: `Briefly describe what this user form structure is for:\n${JSON.stringify(structure, null, 2)}`,
    }).then((desc) => {
      cy.log("AI says:", desc.trim());
    });
  });
});

  });
 
